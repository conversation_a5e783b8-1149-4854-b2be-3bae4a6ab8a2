import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// خدمة إنشاء البيانات الموحدة - تجمع جميع البيانات في وثيقة واحدة
/// هذا يحقق نظام القراءة الواحدة الحقيقي
class UnifiedDataGenerator {
  static final UnifiedDataGenerator _instance = UnifiedDataGenerator._internal();
  static UnifiedDataGenerator get instance => _instance;
  UnifiedDataGenerator._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إنشاء البيانات الموحدة - يجب تشغيلها من تطبيق الأدمن
  Future<bool> generateUnifiedData() async {
    try {
      debugPrint('🔄 بدء إنشاء البيانات الموحدة...');

      // جمع جميع البيانات من المجموعات المختلفة
      final futures = await Future.wait([
        _firestore.collection('sections').get(),
        _firestore.collection('subjects').get(),
        _firestore.collection('video_sections').get(),
        _firestore.collection('video_subjects').get(),
        _firestore.collection('units').get(),
        _firestore.collection('lessons').get(),
        _firestore.collection('questions').get(),
        _firestore.collection('video_units').get(),
        _firestore.collection('video_lessons').get(),
        _firestore.collection('videos').get(),
      ]);

      // تحويل البيانات إلى تنسيق موحد
      final unifiedData = {
        'sections': futures[0].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'subjects': futures[1].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'video_sections': futures[2].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'video_subjects': futures[3].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'units': futures[4].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'lessons': futures[5].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'questions': futures[6].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'video_units': futures[7].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'video_lessons': futures[8].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'videos': futures[9].docs.map((doc) => {
          'id': doc.id,
          ...doc.data(),
        }).toList(),
        'lastUpdated': FieldValue.serverTimestamp(),
        'version': DateTime.now().millisecondsSinceEpoch,
      };

      // حفظ البيانات الموحدة في وثيقة واحدة
      await _firestore
          .collection('unified_data')
          .doc('public_data')
          .set(unifiedData);

      debugPrint('✅ تم إنشاء البيانات الموحدة بنجاح');
      debugPrint('📊 إحصائيات البيانات الموحدة:');
      debugPrint('   - الأقسام: ${(unifiedData['sections'] as List).length}');
      debugPrint('   - المواد: ${(unifiedData['subjects'] as List).length}');
      debugPrint('   - أقسام الفيديو: ${(unifiedData['video_sections'] as List).length}');
      debugPrint('   - مواد الفيديو: ${(unifiedData['video_subjects'] as List).length}');
      debugPrint('   - الوحدات: ${(unifiedData['units'] as List).length}');
      debugPrint('   - الدروس: ${(unifiedData['lessons'] as List).length}');
      debugPrint('   - الأسئلة: ${(unifiedData['questions'] as List).length}');
      debugPrint('   - وحدات الفيديو: ${(unifiedData['video_units'] as List).length}');
      debugPrint('   - دروس الفيديو: ${(unifiedData['video_lessons'] as List).length}');
      debugPrint('   - الفيديوهات: ${(unifiedData['videos'] as List).length}');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء البيانات الموحدة: $e');
      return false;
    }
  }

  /// إنشاء بيانات موحدة لمستخدم محدد (مع الاشتراك)
  Future<bool> generateUserUnifiedData(String deviceId) async {
    try {
      debugPrint('🔄 بدء إنشاء البيانات الموحدة للمستخدم: $deviceId');

      // جلب بيانات اشتراك المستخدم
      final subscriptionDoc = await _firestore
          .collection('user_subscriptions')
          .doc(deviceId)
          .get();

      Map<String, dynamic> subscription = {
        'isActive': false,
        'subjectIds': <String>[],
        'videoSubjectIds': <String>[],
        'activatedAt': null,
        'expiresAt': null,
      };

      if (subscriptionDoc.exists) {
        final subscriptionData = subscriptionDoc.data()!;
        subscription = {
          'isActive': subscriptionData['isActive'] ?? false,
          'subjectIds': List<String>.from(subscriptionData['subjectIds'] ?? []),
          'videoSubjectIds': List<String>.from(subscriptionData['videoSubjectIds'] ?? []),
          'activatedAt': subscriptionData['activatedAt'],
          'expiresAt': subscriptionData['expiresAt'],
        };
      }

      // جلب البيانات العامة
      final publicDataDoc = await _firestore
          .collection('unified_data')
          .doc('public_data')
          .get();

      if (!publicDataDoc.exists) {
        debugPrint('❌ البيانات العامة الموحدة غير موجودة، يجب إنشاؤها أولاً');
        return false;
      }

      final publicData = publicDataDoc.data()!;

      // إنشاء البيانات الموحدة للمستخدم
      final userUnifiedData = {
        'subscription': subscription,
        'sections': publicData['sections'],
        'subjects': publicData['subjects'],
        'video_sections': publicData['video_sections'],
        'video_subjects': publicData['video_subjects'],
        'units': publicData['units'],
        'lessons': publicData['lessons'],
        'questions': publicData['questions'],
        'video_units': publicData['video_units'],
        'video_lessons': publicData['video_lessons'],
        'videos': publicData['videos'],
        'lastUpdated': FieldValue.serverTimestamp(),
        'version': DateTime.now().millisecondsSinceEpoch,
      };

      // حفظ البيانات الموحدة للمستخدم
      await _firestore
          .collection('user_data')
          .doc(deviceId)
          .set(userUnifiedData);

      debugPrint('✅ تم إنشاء البيانات الموحدة للمستخدم بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء البيانات الموحدة للمستخدم: $e');
      return false;
    }
  }

  /// تحديث البيانات الموحدة لجميع المستخدمين
  Future<bool> updateAllUsersUnifiedData() async {
    try {
      debugPrint('🔄 بدء تحديث البيانات الموحدة لجميع المستخدمين...');

      // جلب جميع المستخدمين
      final usersSnapshot = await _firestore
          .collection('user_subscriptions')
          .get();

      int successCount = 0;
      int totalCount = usersSnapshot.docs.length;

      for (final userDoc in usersSnapshot.docs) {
        final success = await generateUserUnifiedData(userDoc.id);
        if (success) successCount++;
      }

      debugPrint('✅ تم تحديث البيانات الموحدة لـ $successCount من $totalCount مستخدم');
      return successCount == totalCount;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات الموحدة للمستخدمين: $e');
      return false;
    }
  }

  /// التحقق من وجود البيانات الموحدة
  Future<bool> checkUnifiedDataExists() async {
    try {
      final doc = await _firestore
          .collection('unified_data')
          .doc('public_data')
          .get();
      return doc.exists;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من البيانات الموحدة: $e');
      return false;
    }
  }

  /// حذف البيانات الموحدة (للاختبار)
  Future<bool> deleteUnifiedData() async {
    try {
      await _firestore
          .collection('unified_data')
          .doc('public_data')
          .delete();
      debugPrint('✅ تم حذف البيانات الموحدة');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف البيانات الموحدة: $e');
      return false;
    }
  }
}
