import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/subscription_code_model.dart';
import '../models/user_subscription_model.dart';
import '../models/subject_model.dart';
import '../models/video_subject_model.dart';
import 'device_service.dart';
import 'admin_user_data_service.dart';
import 'single_read_data_service.dart';

/// خدمة الاشتراك - تعمل مع نظام القراءة الواحدة
class SubscriptionService extends ChangeNotifier {
  static final SubscriptionService _instance = SubscriptionService._internal();
  static SubscriptionService get instance => _instance;
  SubscriptionService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DeviceService _deviceService = DeviceService.instance;
  final AdminUserDataService _adminUserDataService =
      AdminUserDataService.instance;
  final SingleReadDataService _dataService = SingleReadDataService.instance;

  bool _isLoading = false;
  String? _error;

  bool get isLoading => _isLoading;
  String? get error => _error;

  // خصائص للتوافق مع الكود القديم
  List<Subject> get allSubjects => _dataService.subjects;
  List<Subject> get subscribedSubjects => _dataService.getSubscribedSubjects();
  List<Subject> get paidSubjects =>
      _dataService.subjects.where((s) => !s.isFree).toList();
  UserSubscription? get currentSubscription => _dataService.subscription;

  /// تفعيل كود الاشتراك مع تحميل البيانات
  Future<bool> activateSubscriptionCode(String code) async {
    if (_isLoading) return false;

    try {
      _setLoading(true);
      _setError(null);

      debugPrint('🔑 بدء تفعيل كود الاشتراك: $code');

      // الخطوة 1: التحقق من صحة الكود
      final subscriptionCode = await _validateCode(code);
      if (subscriptionCode == null) {
        throw Exception('الكود غير صحيح أو منتهي الصلاحية');
      }

      // الخطوة 2: الحصول على معرف الجهاز
      final deviceId = await _deviceService.getDeviceId();

      // الخطوة 3: التحقق من عدم استخدام الكود مسبقاً
      final existingSubscription = await _getUserSubscription(deviceId);
      if (existingSubscription != null &&
          existingSubscription.hasUsedCode(code)) {
        throw Exception('تم استخدام هذا الكود مسبقاً');
      }

      // الخطوة 4: تحديث حالة الكود
      await _markCodeAsUsed(subscriptionCode, deviceId);

      // الخطوة 5: إنشاء أو تحديث اشتراك المستخدم
      final subscription = await _createOrUpdateSubscription(
        deviceId,
        subscriptionCode,
        existingSubscription,
      );

      // الخطوة 6: تحديث بيانات المستخدم في Firebase
      await _adminUserDataService.updateUserDataAfterSubscription(
        deviceId,
        subscription.toMap(),
      );

      // الخطوة 7: تحديث البيانات المحلية
      await _dataService.performSingleRead();

      debugPrint('✅ تم تفعيل الاشتراك بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تفعيل الاشتراك: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من صحة الكود
  Future<SubscriptionCode?> _validateCode(String code) async {
    try {
      final querySnapshot = await _firestore
          .collection('subscription_codes')
          .where('code', isEqualTo: code)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return null;
      }

      final codeDoc = querySnapshot.docs.first;
      final subscriptionCode = SubscriptionCode.fromMap({
        ...codeDoc.data(),
        'id': codeDoc.id,
      });

      return subscriptionCode.isValid ? subscriptionCode : null;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الكود: $e');
      return null;
    }
  }

  /// الحصول على اشتراك المستخدم الحالي
  Future<UserSubscription?> _getUserSubscription(String deviceId) async {
    try {
      final docSnapshot = await _firestore
          .collection('user_subscriptions')
          .doc(deviceId)
          .get();

      if (!docSnapshot.exists) {
        return null;
      }

      return UserSubscription.fromMap({
        ...docSnapshot.data()!,
        'id': docSnapshot.id,
      });
    } catch (e) {
      debugPrint('❌ خطأ في جلب اشتراك المستخدم: $e');
      return null;
    }
  }

  /// تحديث حالة الكود إلى مستخدم
  Future<void> _markCodeAsUsed(
    SubscriptionCode subscriptionCode,
    String deviceId,
  ) async {
    try {
      await _firestore
          .collection('subscription_codes')
          .doc(subscriptionCode.id)
          .update({
            'status': CodeStatus.used.name,
            'usedByDeviceId': deviceId,
            'usedAt': DateTime.now().millisecondsSinceEpoch,
          });

      debugPrint('✅ تم تحديث حالة الكود إلى مستخدم');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث حالة الكود: $e');
      rethrow;
    }
  }

  /// إنشاء أو تحديث اشتراك المستخدم
  Future<UserSubscription> _createOrUpdateSubscription(
    String deviceId,
    SubscriptionCode subscriptionCode,
    UserSubscription? existingSubscription,
  ) async {
    try {
      final now = DateTime.now();
      final expiresAt =
          subscriptionCode.expiresAt ?? now.add(const Duration(days: 365));

      UserSubscription subscription;

      if (existingSubscription != null) {
        // تحديث الاشتراك الموجود
        final newSubjectIds = <String>{
          ...existingSubscription.subscribedSubjectIds,
          ...subscriptionCode.subjectIds,
        }.toList();

        final newVideoSubjectIds = <String>{
          ...existingSubscription.videoSubjectIds,
          ...subscriptionCode.videoSubjectIds,
        }.toList();

        final newUsedCodes = <String>{
          ...existingSubscription.usedCodes,
          subscriptionCode.code,
        }.toList();

        final newActivationDates = <String, DateTime>{
          ...existingSubscription.subjectActivationDates,
        };

        // إضافة تواريخ تفعيل المواد الجديدة
        for (final subjectId in subscriptionCode.subjectIds) {
          newActivationDates[subjectId] = now;
        }

        subscription = UserSubscription(
          id: existingSubscription.id,
          deviceId: deviceId,
          subscribedSubjectIds: newSubjectIds,
          videoSubjectIds: newVideoSubjectIds,
          subjectActivationDates: newActivationDates,
          usedCodes: newUsedCodes,
          createdAt: existingSubscription.createdAt,
          updatedAt: now,
          expiresAt: expiresAt.isAfter(existingSubscription.expiresAt)
              ? expiresAt
              : existingSubscription.expiresAt,
          isActive: true,
        );
      } else {
        // إنشاء اشتراك جديد
        final activationDates = <String, DateTime>{};
        for (final subjectId in subscriptionCode.subjectIds) {
          activationDates[subjectId] = now;
        }

        subscription = UserSubscription(
          id: deviceId,
          deviceId: deviceId,
          subscribedSubjectIds: subscriptionCode.subjectIds,
          videoSubjectIds: subscriptionCode.videoSubjectIds,
          subjectActivationDates: activationDates,
          usedCodes: [subscriptionCode.code],
          createdAt: now,
          updatedAt: now,
          expiresAt: expiresAt,
          isActive: true,
        );
      }

      // حفظ الاشتراك في Firebase
      await _firestore
          .collection('user_subscriptions')
          .doc(deviceId)
          .set(subscription.toMap());

      debugPrint('✅ تم حفظ اشتراك المستخدم');
      return subscription;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء/تحديث الاشتراك: $e');
      rethrow;
    }
  }

  /// التحقق من حالة الاشتراك
  Future<bool> checkSubscriptionStatus() async {
    try {
      final deviceId = await _deviceService.getDeviceId();
      final subscription = await _getUserSubscription(deviceId);

      if (subscription == null || !subscription.isActive) {
        return false;
      }

      // التحقق من انتهاء الصلاحية
      if (subscription.expiresAt.isBefore(DateTime.now())) {
        // إلغاء تفعيل الاشتراك المنتهي الصلاحية
        await _firestore.collection('user_subscriptions').doc(deviceId).update({
          'isActive': false,
        });
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة الاشتراك: $e');
      return false;
    }
  }

  /// الحصول على معلومات الاشتراك الحالي
  Future<UserSubscription?> getCurrentSubscription() async {
    try {
      final deviceId = await _deviceService.getDeviceId();
      return await _getUserSubscription(deviceId);
    } catch (e) {
      debugPrint('❌ خطأ في جلب الاشتراك الحالي: $e');
      return null;
    }
  }

  /// إلغاء الاشتراك
  Future<bool> cancelSubscription() async {
    try {
      _setLoading(true);

      final deviceId = await _deviceService.getDeviceId();

      // إلغاء تفعيل الاشتراك
      await _firestore.collection('user_subscriptions').doc(deviceId).update({
        'isActive': false,
      });

      // مسح بيانات المستخدم
      await _firestore.collection('user_data').doc(deviceId).delete();

      // مسح البيانات المحلية
      await _dataService.clearAllLocalData();

      debugPrint('✅ تم إلغاء الاشتراك بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الاشتراك: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // ==================== دوال للتوافق مع الكود القديم ====================

  /// تهيئة الخدمة
  Future<void> initialize() async {
    await _dataService.initialize();
  }

  /// التحقق من الاشتراك في مادة
  bool isSubscribedToSubject(String subjectId) {
    return _dataService.isSubscribedToSubject(subjectId);
  }

  /// التحقق من الاشتراك في مادة فيديو
  bool isSubscribedToVideoSubject(String videoSubjectId) {
    return _dataService.isSubscribedToVideoSubject(videoSubjectId);
  }

  /// التحقق من وجود اشتراك نشط
  bool hasActiveSubscription() {
    return _dataService.subscription?.isActive ?? false;
  }

  /// تحميل بيانات الاشتراك
  Future<void> loadUserSubscription() async {
    await _dataService.performSingleRead();
  }

  /// الحصول على مواد الفيديو المشترك بها
  Future<List<VideoSubject>> getSubscribedVideoSubjects() async {
    return _dataService.getSubscribedVideoSubjects();
  }

  /// الحصول على تاريخ تفعيل المادة
  DateTime? getSubjectActivationDate(String subjectId) {
    return _dataService.subscription?.subjectActivationDates[subjectId];
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, String>> getDeviceInfo() async {
    final deviceId = await _deviceService.getDeviceId();
    return {'deviceId': deviceId, 'platform': 'Flutter'};
  }

  /// استخدام كود الاشتراك (نفس activateSubscriptionCode)
  Future<bool> useSubscriptionCode(String code) async {
    return await activateSubscriptionCode(code);
  }
}
