import 'dart:async';
import 'package:flutter/foundation.dart';
import '../repositories/sections_repository.dart';
import '../repositories/subjects_repository.dart';
import '../repositories/subject_content_repository.dart';
import '../repositories/video_sections_repository.dart';
import '../repositories/video_subjects_repository.dart';
import '../repositories/video_content_repository.dart';
import 'subscription_service.dart';

/// خدمة النظام الجديد Offline-First
/// تطبق النظامين الأول والثاني وفقاً لتوجيهات المبرمج العبقري
class OfflineFirstService extends ChangeNotifier {
  static final OfflineFirstService _instance = OfflineFirstService._internal();
  static OfflineFirstService get instance => _instance;
  OfflineFirstService._internal();

  final SectionsRepository _sectionsRepo = SectionsRepository.instance;
  final SubjectsRepository _subjectsRepo = SubjectsRepository.instance;
  final SubjectContentRepository _contentRepo =
      SubjectContentRepository.instance;
  final VideoSectionsRepository _videoSectionsRepo =
      VideoSectionsRepository.instance;
  final VideoSubjectsRepository _videoSubjectsRepo =
      VideoSubjectsRepository.instance;
  final VideoContentRepository _videoContentRepo =
      VideoContentRepository.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  bool _isInitialized = false;
  Timer? _periodicSyncTimer;

  /// النظام الأول: تهيئة الأقسام والمواد
  /// يحمل جميع الأقسام والمواد دفعة واحدة ويعرضها للجميع (حتى غير المشتركين)
  Future<void> initializeBasicData() async {
    try {
      debugPrint('🚀 بدء النظام الأول - تهيئة الأقسام والمواد...');

      // تهيئة الأقسام
      await _sectionsRepo.initialize();

      // تهيئة المواد
      await _subjectsRepo.initialize();

      // تهيئة أقسام الفيديوهات
      await _videoSectionsRepo.initialize();

      // تهيئة مواد الفيديوهات
      await _videoSubjectsRepo.initialize();

      debugPrint('✅ تم تهيئة النظام الأول بنجاح');
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة النظام الأول: $e');
    }
  }

  /// النظام الثاني: تحميل محتوى المواد المشترك فيها
  /// يحمل الوحدات والدروس والأسئلة فقط للمواد المشترك فيها
  Future<void> loadSubscribedContent() async {
    try {
      debugPrint('🚀 بدء النظام الثاني - تحميل محتوى المواد المشترك فيها...');

      // الحصول على المواد المشترك فيها
      final subscribedSubjects = _subscriptionService.subscribedSubjects;

      if (subscribedSubjects.isEmpty) {
        debugPrint('ℹ️ لا توجد مواد مشترك فيها');
        return;
      }

      debugPrint(
        '📚 تحميل محتوى ${subscribedSubjects.length} مادة مشترك فيها...',
      );

      // تحميل محتوى كل مادة مشترك فيها
      for (final subject in subscribedSubjects) {
        await _contentRepo.loadSubjectContent(subject.id);
      }

      // تنظيف بيانات المواد غير المشترك فيها
      await _contentRepo.cleanupUnsubscribedSubjects();

      debugPrint('✅ تم تحميل محتوى المواد المشترك فيها بنجاح');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل محتوى المواد المشترك فيها: $e');
    }
  }

  /// النظام الثاني للفيديوهات: تحميل محتوى مواد الفيديوهات المشترك فيها
  /// يحمل وحدات ودروس وفيديوهات فقط للمواد المشترك فيها
  Future<void> loadSubscribedVideoContent() async {
    try {
      debugPrint(
        '🚀 بدء النظام الثاني للفيديوهات - تحميل محتوى مواد الفيديوهات المشترك فيها...',
      );

      // الحصول على مواد الفيديوهات المشترك فيها
      final subscribedVideoSubjects = _subscriptionService
          .getAllowedVideoSubjectIds();

      if (subscribedVideoSubjects.isEmpty) {
        debugPrint('ℹ️ لا توجد مواد فيديوهات مشترك فيها');
        return;
      }

      debugPrint(
        '📹 تحميل محتوى ${subscribedVideoSubjects.length} مادة فيديو مشترك فيها...',
      );

      // تحميل محتوى كل مادة فيديو مشترك فيها
      for (final subjectId in subscribedVideoSubjects) {
        await _videoContentRepo.loadVideoSubjectContent(subjectId);
      }

      debugPrint('✅ تم تحميل محتوى مواد الفيديوهات المشترك فيها بنجاح');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل محتوى مواد الفيديوهات المشترك فيها: $e');
    }
  }

  /// تهيئة النظام الكامل
  Future<void> initialize() async {
    try {
      debugPrint('🚀 تهيئة النظام الجديد Offline-First...');

      // النظام الأول: الأقسام والمواد
      await initializeBasicData();

      // النظام الثاني: محتوى المواد المشترك فيها
      await loadSubscribedContent();

      // النظام الثاني للفيديوهات: محتوى مواد الفيديوهات المشترك فيها
      await loadSubscribedVideoContent();

      // بدء المزامنة الدورية
      _startPeriodicSync();

      debugPrint('✅ تم تهيئة النظام الجديد بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة النظام الجديد: $e');
    }
  }

  /// بدء المزامنة الدورية في الخلفية
  void _startPeriodicSync() {
    // إيقاف المؤقت السابق إن وجد
    _periodicSyncTimer?.cancel();

    // بدء مؤقت جديد للمزامنة كل 30 دقيقة
    _periodicSyncTimer = Timer.periodic(
      const Duration(minutes: 30),
      (timer) => _performPeriodicSync(),
    );

    debugPrint('⏰ تم بدء المزامنة الدورية (كل 30 دقيقة)');
  }

  /// تنفيذ المزامنة الدورية
  Future<void> _performPeriodicSync() async {
    try {
      debugPrint('🔄 بدء المزامنة الدورية...');

      // مزامنة الأقسام والمواد
      await _sectionsRepo.periodicSync();
      await _subjectsRepo.periodicSync();

      // مزامنة أقسام ومواد الفيديوهات
      await _videoSectionsRepo.periodicSync();
      await _videoSubjectsRepo.periodicSync();

      // مزامنة محتوى المواد المشترك فيها
      await _contentRepo.syncAllSubscribedSubjects();

      // مزامنة محتوى مواد الفيديوهات المشترك فيها
      await _videoContentRepo.syncAllSubscribedVideoSubjects();

      debugPrint('✅ تم إنجاز المزامنة الدورية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الدورية: $e');
    }
  }

  /// فرض إعادة تحميل جميع البيانات
  Future<void> forceRefreshAll() async {
    try {
      debugPrint('🔄 فرض إعادة تحميل جميع البيانات...');

      // إعادة تحميل الأقسام والمواد
      await _sectionsRepo.forceRefresh();
      await _subjectsRepo.forceRefresh();

      // إعادة تحميل محتوى المواد المشترك فيها
      await _contentRepo.syncAllSubscribedSubjects();

      debugPrint('✅ تم إعادة تحميل جميع البيانات بنجاح');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل البيانات: $e');
    }
  }

  /// تحديث الاشتراكات وإعادة تحميل المحتوى
  Future<void> onSubscriptionChanged() async {
    try {
      debugPrint('🔄 تحديث الاشتراكات وإعادة تحميل المحتوى...');

      // إعادة تحميل محتوى المواد الجديدة المشترك فيها
      await loadSubscribedContent();

      debugPrint('✅ تم تحديث المحتوى بناءً على الاشتراكات الجديدة');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المحتوى بعد تغيير الاشتراك: $e');
    }
  }

  /// الحصول على الأقسام النشطة (فوري من الذاكرة)
  Future<List<dynamic>> getActiveSections() async {
    return await _sectionsRepo.getActiveSections();
  }

  /// الحصول على المواد النشطة لقسم معين (فوري من الذاكرة)
  Future<List<dynamic>> getActiveSubjectsBySection(String sectionId) async {
    return await _subjectsRepo.getSubjectsBySection(sectionId);
  }

  /// الحصول على المواد المدفوعة لقسم معين (فوري من الذاكرة)
  Future<List<dynamic>> getPaidSubjectsBySection(String sectionId) async {
    return await _subjectsRepo.getPaidSubjectsBySection(sectionId);
  }

  /// الحصول على وحدات مادة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getSubjectUnits(String subjectId) async {
    return await _contentRepo.getSubjectUnits(subjectId);
  }

  /// الحصول على دروس مادة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getSubjectLessons(String subjectId) async {
    return await _contentRepo.getSubjectLessons(subjectId);
  }

  /// الحصول على أسئلة مادة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getSubjectQuestions(String subjectId) async {
    return await _contentRepo.getSubjectQuestions(subjectId);
  }

  /// الحصول على دروس وحدة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getUnitLessons(String unitId) async {
    return await _contentRepo.getUnitLessons(unitId);
  }

  /// الحصول على أسئلة وحدة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getUnitQuestions(String unitId) async {
    return await _contentRepo.getUnitQuestions(unitId);
  }

  /// الحصول على أسئلة درس معين (فوري من الذاكرة)
  Future<List<dynamic>> getLessonQuestions(String lessonId) async {
    return await _contentRepo.getLessonQuestions(lessonId);
  }

  /// البحث في الأقسام (فوري من الذاكرة)
  Future<List<dynamic>> searchSections(String query) async {
    return await _sectionsRepo.searchSections(query);
  }

  // ==================== أقسام الفيديو ====================

  /// الحصول على أقسام الفيديو النشطة (فوري من الذاكرة)
  Future<List<dynamic>> getActiveVideoSections() async {
    return await _videoSectionsRepo.getActiveVideoSections();
  }

  /// البحث في أقسام الفيديو (فوري من الذاكرة)
  Future<List<dynamic>> searchVideoSections(String query) async {
    return await _videoSectionsRepo.searchVideoSections(query);
  }

  /// البحث في المواد (فوري من الذاكرة)
  Future<List<dynamic>> searchSubjects(String query) async {
    return await _subjectsRepo.searchSubjects(query);
  }

  /// الحصول على إحصائيات النظام
  Future<Map<String, dynamic>> getSystemStats() async {
    try {
      final sectionsStats = {
        'sections': (await _sectionsRepo.getActiveSections()).length,
      };

      final subjectsStats = await _subjectsRepo.getSubjectsStats();

      final subscribedSubjects = _subscriptionService.subscribedSubjects;

      return {
        ...sectionsStats,
        ...subjectsStats,
        'subscribedSubjects': subscribedSubjects.length,
        'isInitialized': _isInitialized,
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات النظام: $e');
      return {};
    }
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    _periodicSyncTimer?.cancel();
    super.dispose();
  }

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}
