import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/lesson_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class CourseLessonsSelectionPage extends StatefulWidget {
  final Subject subject;

  const CourseLessonsSelectionPage({super.key, required this.subject});

  @override
  State<CourseLessonsSelectionPage> createState() =>
      _CourseLessonsSelectionPageState();
}

class _CourseLessonsSelectionPageState
    extends State<CourseLessonsSelectionPage> {
  List<Lesson> _lessonsWithCourseQuestions = [];
  final Map<String, int> _lessonQuestionCounts = {};
  final Map<String, String> _lessonUnitNames = {};

  @override
  void initState() {
    super.initState();
    _loadCourseLessonsImmediately();
  }

  /// تحميل فوري لدروس الدورات من النظام الجديد
  Future<void> _loadCourseLessonsImmediately() async {
    try {
      // استخدام النظام القديم مؤقتاً حتى يتم تطبيق النظام الجديد بالكامل
      await _loadLessonsWithCourseQuestions();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل دروس الدورات: $e');
    }
  }

  Future<void> _loadLessonsWithCourseQuestions() async {
    try {
      debugPrint(
        '🔍 فحص الدروس التي تحتوي على أسئلة دورات للمادة: ${widget.subject.name}',
      );

      // تحميل جميع وحدات المادة (من التخزين المحلي أولاً)
      final allUnits = await ContentService.instance.getSubjectUnits(
        widget.subject.id,
      );

      if (allUnits.isEmpty) {
        setState(() {
          _lessonsWithCourseQuestions = [];
          // لا نغير _isLoading إذا لم توجد بيانات
        });
        return;
      }

      List<Lesson> lessonsWithQuestions = [];

      for (final unit in allUnits) {
        // تحميل دروس الوحدة (من التخزين المحلي أولاً)
        final unitLessons = await ContentService.instance.getUnitLessons(
          unit.id,
        );

        for (final lesson in unitLessons) {
          // تحقق من وجود أسئلة دورات في هذا الدرس (من التخزين المحلي أولاً)
          final courseQuestions = await ExamService.instance
              .getQuestionsByLesson(
                lesson.id,
                true, // أسئلة دورات فقط
              );

          if (courseQuestions.isNotEmpty) {
            lessonsWithQuestions.add(lesson);
            _lessonQuestionCounts[lesson.id] = courseQuestions.length;
            _lessonUnitNames[lesson.id] = unit.name;
            debugPrint(
              '✅ الدرس ${lesson.name} في الوحدة ${unit.name} يحتوي على ${courseQuestions.length} سؤال دورة',
            );
          }
        }
      }

      setState(() {
        _lessonsWithCourseQuestions = lessonsWithQuestions;
        // لا نغير _isLoading إذا كانت البيانات متوفرة محلياً
      });

      debugPrint(
        '📊 تم العثور على ${lessonsWithQuestions.length} درس يحتوي على أسئلة دورات',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الدروس: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'دورات ${widget.subject.name} - حسب الدروس',
          style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.secondaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _lessonsWithCourseQuestions.isEmpty
          ? _buildEmptyState()
          : _buildLessonsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.play_lesson_outlined, size: 80.w, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد دروس تحتوي على أسئلة دورات',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'لم يتم إنشاء أي أسئلة دورات لهذه المادة بعد',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLessonsList() {
    return RefreshIndicator(
      onRefresh: _loadLessonsWithCourseQuestions,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _lessonsWithCourseQuestions.length,
        itemBuilder: (context, index) {
          final lesson = _lessonsWithCourseQuestions[index];
          final questionCount = _lessonQuestionCounts[lesson.id] ?? 0;
          final unitName = _lessonUnitNames[lesson.id] ?? '';

          return Card(
            margin: EdgeInsets.only(bottom: 12.h),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 8.h,
              ),
              leading: Container(
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  color: AppTheme.secondaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.play_lesson,
                  color: AppTheme.secondaryColor,
                  size: 24.w,
                ),
              ),
              title: Text(
                lesson.name,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الوحدة: $unitName',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    '$questionCount سؤال دورة',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.secondaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16.w,
              ),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => QuestionsViewerPage(
                      title: 'دورات ${lesson.name}',
                      subject: widget.subject,
                      lessonId: lesson.id,
                      questionType: QuestionFilterType.courseByLesson,
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
