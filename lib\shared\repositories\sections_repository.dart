import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../core/models/section.dart';
import '../services/simple_data_service.dart';
import 'base_repository.dart';

/// Repository للأقسام مع دعم Offline-First
class SectionsRepository extends BaseRepository<Section> {
  static final SectionsRepository _instance = SectionsRepository._internal();
  static SectionsRepository get instance => _instance;
  SectionsRepository._internal();

  @override
  String get collectionName => 'sections';

  @override
  String get storageKey => 'sections';

  @override
  Section fromFirestore(Map<String, dynamic> data, String documentId) {
    return Section(
      id: documentId,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      color: data['color'] ?? '#6C5CE7',
      isActive: data['isActive'] ?? true,
      isFree: data['isFree'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  Map<String, dynamic> toLocalMap(Section item) {
    return {
      'id': item.id,
      'name': item.name,
      'description': item.description,
      'color': item.color,
      'isActive': item.isActive,
      'isFree': item.isFree,
      'createdAt': item.createdAt.toIso8601String(),
      'updatedAt': item.updatedAt.toIso8601String(),
    };
  }

  @override
  Section fromLocalMap(Map<String, dynamic> map) {
    return Section(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      isActive: map['isActive'] ?? true,
      isFree: map['isFree'] ?? false,
      createdAt: DateTime.parse(
        map['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        map['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  @override
  Map<String, dynamic> toFirestore(Section item) {
    return {
      'name': item.name,
      'description': item.description,
      'color': item.color,
      'isActive': item.isActive,
      'isFree': item.isFree,
      'createdAt': Timestamp.fromDate(item.createdAt),
      'updatedAt': Timestamp.fromDate(item.updatedAt),
    };
  }

  @override
  Future<List<Section>> _loadFromLocalStorage() async {
    try {
      final file = File(
        '${persistentStorage.storageDirectory.path}/sections.json',
      );
      if (!await file.exists()) {
        // إذا لم توجد بيانات محلية، استخدم البيانات الافتراضية
        final defaultSections = SimpleDataService.instance.getSections();
        debugPrint(
          '📱 تم تحميل ${defaultSections.length} قسم من البيانات الافتراضية',
        );
        return defaultSections;
      }

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final sections = data.map((item) => fromLocalMap(item)).toList();

      debugPrint('📱 تم تحميل ${sections.length} قسم من التخزين المحلي');
      return sections;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام من التخزين المحلي: $e');
      // في حالة الخطأ، إرجاع البيانات الافتراضية
      return SimpleDataService.instance.getSections();
    }
  }

  @override
  Future<void> _saveToLocalStorage(List<Section> sections) async {
    try {
      final file = File(
        '${persistentStorage.storageDirectory.path}/sections.json',
      );
      final data = sections.map((section) => toLocalMap(section)).toList();
      await file.writeAsString(jsonEncode(data));

      debugPrint('✅ تم حفظ ${sections.length} قسم في التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الأقسام في التخزين المحلي: $e');
    }
  }

  /// الحصول على الأقسام النشطة فقط
  Future<List<Section>> getActiveSections() async {
    final allSections = await getLocalData();
    return allSections.where((section) => section.isActive).toList();
  }

  /// الحصول على الأقسام المدفوعة النشطة
  Future<List<Section>> getPaidActiveSections() async {
    final allSections = await getLocalData();
    return allSections
        .where((section) => section.isActive && !section.isFree)
        .toList();
  }

  /// الحصول على الأقسام المجانية النشطة
  Future<List<Section>> getFreeActiveSections() async {
    final allSections = await getLocalData();
    return allSections
        .where((section) => section.isActive && section.isFree)
        .toList();
  }

  /// البحث في الأقسام
  Future<List<Section>> searchSections(String query) async {
    final allSections = await getLocalData();
    final lowerQuery = query.toLowerCase();

    return allSections
        .where(
          (section) =>
              section.name.toLowerCase().contains(lowerQuery) ||
              section.description.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Section?> getSectionById(String id) async {
    final allSections = await getLocalData();
    try {
      return allSections.firstWhere((section) => section.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تهيئة البيانات الأولية
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة repository الأقسام...');

      // تحميل البيانات المحلية أولاً
      final localSections = await getLocalData();
      debugPrint('📱 تم تحميل ${localSections.length} قسم محلياً');

      // التحقق من التحديثات في الخلفية
      _checkForUpdatesInBackground();

      debugPrint('✅ تم تهيئة repository الأقسام بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة repository الأقسام: $e');
    }
  }

  /// التحقق من التحديثات في الخلفية
  void _checkForUpdatesInBackground() {
    Future.delayed(Duration.zero, () async {
      try {
        if (await hasRemoteUpdates()) {
          debugPrint('🔄 يوجد تحديثات جديدة للأقسام، بدء المزامنة...');
          await fetchAndCacheRemoteData();
        }
      } catch (e) {
        debugPrint('❌ خطأ في التحقق من تحديثات الأقسام: $e');
      }
    });
  }

  /// مزامنة دورية (يمكن استدعاؤها من خدمة المزامنة في الخلفية)
  Future<void> periodicSync() async {
    try {
      debugPrint('🔄 مزامنة دورية للأقسام...');
      await syncIfNeeded();
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الدورية للأقسام: $e');
    }
  }
}
