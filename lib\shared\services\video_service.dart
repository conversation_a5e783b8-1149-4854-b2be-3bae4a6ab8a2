// ملف وهمي مؤقت - يحتوي على جميع الدوال المطلوبة للتوافق
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';

export 'simple_video_service.dart';

// كلاس وهمي للتوافق مع الكود القديم
class VideoService {
  static VideoService get instance => VideoService();

  // دوال التهيئة والتحميل
  Future<void> initialize() async {}
  Future<void> loadVideoSections() async {}
  Future<void> loadVideoSubjects() async {}
  Future<void> loadVideoUnits() async {}
  Future<void> loadVideoLessons() async {}
  Future<void> loadVideos() async {}

  // دوال الحصول على البيانات
  Future<List<VideoSection>> getVideoSections() async => [];
  Future<List<VideoSubject>> getAllVideoSubjects() async => [];
  Future<List<VideoSubject>> refreshVideoSubjectsFromFirebase(
    String sectionId,
  ) async => [];
  Future<List<VideoUnit>> getVideoUnits(String subjectId) async => [];
  Future<List<VideoUnit>> refreshVideoUnitsFromFirebase(
    String subjectId,
  ) async => [];
  Future<List<VideoLesson>> getVideoLessons(String unitId) async => [];
  Future<List<VideoLesson>> refreshVideoLessonsFromFirebase(
    String unitId,
  ) async => [];
  Future<List<Video>> refreshVideosFromFirebase(String lessonId) async => [];
  Future<List<VideoSection>> getFreeVideoSections() async => [];
  Future<List<VideoSection>> getPaidVideoSections() async => [];
  Future<void> refreshVideoSectionsFromFirebase() async {}
  dynamic get persistentStorage => null;

  // دوال التخزين المؤقت
  List<VideoUnit> getCachedVideoUnits(String subjectId) => [];
  List<VideoLesson> getCachedVideoLessons(String unitId) => [];

  // دوال التحديث والحذف
  Future<void> addVideoSection(VideoSection section) async {}
  Future<void> updateVideoSection(VideoSection section) async {}
  Future<void> deleteVideoSection(String sectionId) async {}
  Future<void> addVideoSubject(VideoSubject subject) async {}
  Future<void> updateVideoSubject(VideoSubject subject) async {}
  Future<void> deleteVideoSubject(String subjectId) async {}
  Future<void> addVideoUnit(VideoUnit unit) async {}
  Future<void> updateVideoUnit(VideoUnit unit) async {}
  Future<void> deleteVideoUnit(String unitId) async {}
  Future<void> addVideoLesson(VideoLesson lesson) async {}
  Future<void> updateVideoLesson(VideoLesson lesson) async {}
  Future<void> deleteVideoLesson(String lessonId) async {}
  Future<void> addVideo(Video video) async {}
  Future<void> updateVideo(Video video) async {}
  Future<void> deleteVideo(String videoId) async {}

  // دوال التحميل والتشغيل
  Future<List<String>> getDownloadedQualities(String videoId) async => [];
  Future<bool> isVideoDownloadedWithQuality(
    String videoId,
    String quality,
  ) async => false;
  Future<String?> getDecryptedVideoForPlayback(
    String videoId,
    String quality,
  ) async => null;
  Future<bool> downloadVideo(
    String videoId,
    String quality, {
    Function(double)? onProgress,
  }) async => false;
  Future<bool> deleteDownloadedVideo(String videoId, String quality) async =>
      false;
  Future<bool> deleteAllDownloadedQualities(String videoId) async => false;
  void cancelDownload(String videoId) {}
  void cleanupTempFiles() {}

  // دوال المراقبة
  void addVideoListener(Function listener) {}
  void removeVideoListener(Function listener) {}
}
