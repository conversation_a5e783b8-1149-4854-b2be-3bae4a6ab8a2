// ملف وهمي مؤقت - سيتم استبداله بالنظام الجديد
export 'simple_video_service.dart';

// إعادة تصدير الكلاس بالاسم القديم للتوافق المؤقت
class VideoService {
  static VideoService get instance => VideoService();
  
  Future<void> initialize() async {}
  Future<void> loadVideoSections() async {}
  Future<void> loadVideoSubjects() async {}
  Future<void> loadVideoUnits() async {}
  Future<void> loadVideoLessons() async {}
  Future<void> loadVideos() async {}
}
