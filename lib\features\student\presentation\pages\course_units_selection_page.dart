import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/models/unit_model.dart';
import '../../../../shared/services/content_service.dart';
import '../../../../shared/services/exam_service.dart';
import 'questions_viewer_page.dart';
import 'subject_units_page.dart';

class CourseUnitsSelectionPage extends StatefulWidget {
  final Subject subject;

  const CourseUnitsSelectionPage({super.key, required this.subject});

  @override
  State<CourseUnitsSelectionPage> createState() =>
      _CourseUnitsSelectionPageState();
}

class _CourseUnitsSelectionPageState extends State<CourseUnitsSelectionPage> {
  List<Unit> _unitsWithCourseQuestions = [];
  final Map<String, int> _unitQuestionCounts = {};

  @override
  void initState() {
    super.initState();
    _loadUnitsWithCourseQuestionsImmediately();
  }

  /// تحميل فوري للوحدات من النظام الجديد
  Future<void> _loadUnitsWithCourseQuestionsImmediately() async {
    try {
      // استخدام النظام القديم مؤقتاً حتى يتم تطبيق النظام الجديد بالكامل
      await _loadUnitsWithCourseQuestions();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل وحدات الدورات: $e');
    }
  }

  Future<void> _loadUnitsWithCourseQuestions() async {
    try {
      debugPrint(
        '🔍 فحص الوحدات التي تحتوي على أسئلة دورات للمادة: ${widget.subject.name}',
      );

      // تحميل جميع وحدات المادة (من التخزين المحلي أولاً)
      final allUnits = await ContentService.instance.getSubjectUnits(
        widget.subject.id,
      );

      if (allUnits.isEmpty) {
        setState(() {
          _unitsWithCourseQuestions = [];
          // لا نغير _isLoading إذا لم توجد بيانات
        });
        return;
      }

      List<Unit> unitsWithQuestions = [];

      for (final unit in allUnits) {
        // تحقق من وجود أسئلة دورات في هذه الوحدة (من التخزين المحلي أولاً)
        final courseQuestions = await ExamService.instance.getQuestionsByUnit(
          unit.id,
          true, // أسئلة دورات فقط
        );

        if (courseQuestions.isNotEmpty) {
          unitsWithQuestions.add(unit);
          _unitQuestionCounts[unit.id] = courseQuestions.length;
          debugPrint(
            '✅ الوحدة ${unit.name} تحتوي على ${courseQuestions.length} سؤال دورة',
          );
        }
      }

      setState(() {
        _unitsWithCourseQuestions = unitsWithQuestions;
        // لا نغير _isLoading إذا كانت البيانات متوفرة محلياً
      });

      debugPrint(
        '📊 تم العثور على ${unitsWithQuestions.length} وحدة تحتوي على أسئلة دورات',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الوحدات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'دورات ${widget.subject.name} - حسب الوحدات',
          style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _unitsWithCourseQuestions.isEmpty
          ? _buildEmptyState()
          : _buildUnitsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.folder_off, size: 80.w, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد وحدات تحتوي على أسئلة دورات',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'لم يتم إنشاء أي أسئلة دورات لهذه المادة بعد',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUnitsList() {
    return RefreshIndicator(
      onRefresh: _loadUnitsWithCourseQuestions,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: _unitsWithCourseQuestions.length,
        itemBuilder: (context, index) {
          final unit = _unitsWithCourseQuestions[index];
          final questionCount = _unitQuestionCounts[unit.id] ?? 0;

          return Card(
            margin: EdgeInsets.only(bottom: 12.h),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: ListTile(
              contentPadding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 8.h,
              ),
              leading: Container(
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.folder_special,
                  color: AppTheme.primaryColor,
                  size: 24.w,
                ),
              ),
              title: Text(
                unit.name,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              subtitle: Text(
                '$questionCount سؤال دورة',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey[400],
                size: 16.w,
              ),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => QuestionsViewerPage(
                      title: 'دورات ${unit.name}',
                      subject: widget.subject,
                      unitId: unit.id,
                      questionType: QuestionFilterType.courseByUnit,
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
