import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../services/persistent_storage_service.dart';
import '../services/subscription_service.dart';
import '../services/local_notifications_service.dart';

/// Repository لمحتوى الفيديوهات (وحدات، دروس، فيديوهات) مع دعم Offline-First
/// يحمل المحتوى فقط للمواد المشترك فيها مع الحفاظ على التشفير
class VideoContentRepository extends ChangeNotifier {
  static final VideoContentRepository _instance =
      VideoContentRepository._internal();
  static VideoContentRepository get instance => _instance;
  VideoContentRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final LocalNotificationsService _notificationsService =
      LocalNotificationsService.instance;

  /// تحميل محتوى مادة فيديو معينة (وحدات، دروس، فيديوهات) دفعة واحدة
  Future<Map<String, dynamic>> loadVideoSubjectContent(String subjectId) async {
    try {
      debugPrint('🔄 تحميل محتوى مادة الفيديو: $subjectId');

      // التحقق من الاشتراك أولاً
      if (!_subscriptionService.isSubscribedToVideoSubject(subjectId)) {
        debugPrint('❌ المستخدم غير مشترك في مادة الفيديو: $subjectId');
        return {
          'units': <VideoUnit>[],
          'lessons': <VideoLesson>[],
          'videos': <Video>[],
        };
      }

      // محاولة تحميل البيانات المحلية أولاً
      final localContent = await _loadLocalVideoSubjectContent(subjectId);

      // التحقق من التحديثات في الخلفية
      _checkVideoSubjectUpdatesInBackground(subjectId);

      return localContent;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل محتوى مادة الفيديو: $e');
      return {
        'units': <VideoUnit>[],
        'lessons': <VideoLesson>[],
        'videos': <Video>[],
      };
    }
  }

  /// تحميل البيانات المحلية لمادة فيديو معينة
  Future<Map<String, dynamic>> _loadLocalVideoSubjectContent(
    String subjectId,
  ) async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // تحميل وحدات الفيديو
      final unitsFile = File(
        '${storageDir.path}/video_subject_${subjectId}_units.json',
      );
      List<VideoUnit> units = [];
      if (await unitsFile.exists()) {
        final unitsContent = await unitsFile.readAsString();
        final List<dynamic> unitsData = jsonDecode(unitsContent);
        units = unitsData.map((item) => VideoUnit.fromLocalMap(item)).toList();
      }

      // تحميل دروس الفيديو
      final lessonsFile = File(
        '${storageDir.path}/video_subject_${subjectId}_lessons.json',
      );
      List<VideoLesson> lessons = [];
      if (await lessonsFile.exists()) {
        final lessonsContent = await lessonsFile.readAsString();
        final List<dynamic> lessonsData = jsonDecode(lessonsContent);
        lessons = lessonsData
            .map((item) => VideoLesson.fromLocalMap(item))
            .toList();
      }

      // تحميل الفيديوهات
      final videosFile = File(
        '${storageDir.path}/video_subject_${subjectId}_videos.json',
      );
      List<Video> videos = [];
      if (await videosFile.exists()) {
        final videosContent = await videosFile.readAsString();
        final List<dynamic> videosData = jsonDecode(videosContent);
        videos = videosData.map((item) => Video.fromLocalMap(item)).toList();
      }

      debugPrint('📱 تم تحميل محتوى مادة الفيديو $subjectId محلياً:');
      debugPrint('   - ${units.length} وحدة فيديو');
      debugPrint('   - ${lessons.length} درس فيديو');
      debugPrint('   - ${videos.length} فيديو');

      return {'units': units, 'lessons': lessons, 'videos': videos};
    } catch (e) {
      debugPrint(
        '❌ خطأ في تحميل البيانات المحلية لمادة الفيديو $subjectId: $e',
      );
      return {
        'units': <VideoUnit>[],
        'lessons': <VideoLesson>[],
        'videos': <Video>[],
      };
    }
  }

  /// حفظ محتوى مادة الفيديو محلياً
  Future<void> _saveVideoSubjectContentLocally(
    String subjectId,
    List<VideoUnit> units,
    List<VideoLesson> lessons,
    List<Video> videos,
  ) async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // حفظ وحدات الفيديو
      final unitsFile = File(
        '${storageDir.path}/video_subject_${subjectId}_units.json',
      );
      final unitsData = units.map((unit) => unit.toLocalMap()).toList();
      await unitsFile.writeAsString(jsonEncode(unitsData));

      // حفظ دروس الفيديو
      final lessonsFile = File(
        '${storageDir.path}/video_subject_${subjectId}_lessons.json',
      );
      final lessonsData = lessons.map((lesson) => lesson.toLocalMap()).toList();
      await lessonsFile.writeAsString(jsonEncode(lessonsData));

      // حفظ الفيديوهات (مع الحفاظ على التشفير)
      final videosFile = File(
        '${storageDir.path}/video_subject_${subjectId}_videos.json',
      );
      final videosData = videos.map((video) => video.toLocalMap()).toList();
      await videosFile.writeAsString(jsonEncode(videosData));

      // حفظ وقت التحديث
      await _persistentStorage.setLastUpdate(
        'video_subject_content_$subjectId',
        DateTime.now(),
      );

      debugPrint('✅ تم حفظ محتوى مادة الفيديو $subjectId محلياً:');
      debugPrint('   - ${units.length} وحدة فيديو');
      debugPrint('   - ${lessons.length} درس فيديو');
      debugPrint('   - ${videos.length} فيديو');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ محتوى مادة الفيديو محلياً: $e');
    }
  }

  /// التحقق من تحديثات مادة الفيديو في الخلفية
  void _checkVideoSubjectUpdatesInBackground(String subjectId) {
    Future.delayed(Duration.zero, () async {
      try {
        if (await _hasVideoSubjectUpdates(subjectId)) {
          debugPrint(
            '🔄 يوجد تحديثات جديدة لمادة الفيديو $subjectId، بدء المزامنة...',
          );
          await _fetchAndCacheVideoSubjectContent(subjectId);
        }
      } catch (e) {
        debugPrint('❌ خطأ في التحقق من تحديثات مادة الفيديو $subjectId: $e');
      }
    });
  }

  /// التحقق من وجود تحديثات لمادة الفيديو
  Future<bool> _hasVideoSubjectUpdates(String subjectId) async {
    try {
      final lastLocalUpdate = await _persistentStorage.getLastUpdate(
        'video_subject_content_$subjectId',
      );
      if (lastLocalUpdate == null) return true;

      // التحقق من آخر تحديث في Firebase
      final metadataDoc = await _firestore
          .collection('subject_videos_metadata')
          .doc(subjectId)
          .get();

      if (!metadataDoc.exists) {
        debugPrint('❌ لا توجد بيانات وصفية لمادة الفيديو: $subjectId');
        return false;
      }

      final remoteLastUpdate = (metadataDoc.data()!['lastUpdate'] as Timestamp)
          .toDate();
      final hasUpdates = remoteLastUpdate.isAfter(lastLocalUpdate);

      debugPrint('🔍 التحقق من تحديثات مادة الفيديو: $subjectId');
      debugPrint('📅 آخر تحديث محلي: $lastLocalUpdate');
      debugPrint('📅 آخر تحديث بعيد: $remoteLastUpdate');
      debugPrint('🔄 يوجد تحديثات: $hasUpdates');

      return hasUpdates;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من تحديثات مادة الفيديو: $e');
      return false;
    }
  }

  /// جلب وحفظ محتوى مادة الفيديو من Firebase
  Future<Map<String, dynamic>> _fetchAndCacheVideoSubjectContent(
    String subjectId,
  ) async {
    try {
      debugPrint('🔄 جلب محتوى مادة الفيديو من Firebase: $subjectId');

      // تحميل المحتوى القديم للمقارنة
      final oldContent = await _loadLocalVideoSubjectContent(subjectId);

      // جلب وحدات الفيديو
      final unitsSnapshot = await _firestore
          .collection('video_units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final units = unitsSnapshot.docs
          .map((doc) => VideoUnit.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      // جلب دروس الفيديو
      final lessonsSnapshot = await _firestore
          .collection('video_lessons')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final lessons = lessonsSnapshot.docs
          .map((doc) => VideoLesson.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      // جلب الفيديوهات (مع الحفاظ على التشفير)
      final videosSnapshot = await _firestore
          .collection('videos')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final videos = videosSnapshot.docs
          .map((doc) => Video.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      final newContent = {'units': units, 'lessons': lessons, 'videos': videos};

      // إنشاء إشعار إذا كان هناك تغييرات
      await _createVideoUpdateNotificationIfNeeded(
        subjectId,
        oldContent,
        newContent,
      );

      // حفظ البيانات محلياً
      await _saveVideoSubjectContentLocally(subjectId, units, lessons, videos);

      debugPrint('✅ تم جلب وحفظ محتوى مادة الفيديو $subjectId:');
      debugPrint('   - ${units.length} وحدة فيديو');
      debugPrint('   - ${lessons.length} درس فيديو');
      debugPrint('   - ${videos.length} فيديو');

      notifyListeners();

      return newContent;
    } catch (e) {
      debugPrint('❌ خطأ في جلب محتوى مادة الفيديو من Firebase: $e');
      return await _loadLocalVideoSubjectContent(subjectId);
    }
  }

  /// إنشاء إشعار تحديث الفيديو إذا كان هناك تغييرات
  Future<void> _createVideoUpdateNotificationIfNeeded(
    String subjectId,
    Map<String, dynamic> oldContent,
    Map<String, dynamic> newContent,
  ) async {
    try {
      // الحصول على اسم مادة الفيديو
      final videoSubjects = _subscriptionService.getAllowedVideoSubjectIds();
      if (!videoSubjects.contains(subjectId)) return;

      // حساب التغييرات
      final changes = <String, int>{};

      final oldUnits = oldContent['units'] as List? ?? [];
      final newUnits = newContent['units'] as List? ?? [];
      final unitsDiff = newUnits.length - oldUnits.length;
      if (unitsDiff != 0) changes['video_units'] = unitsDiff;

      final oldLessons = oldContent['lessons'] as List? ?? [];
      final newLessons = newContent['lessons'] as List? ?? [];
      final lessonsDiff = newLessons.length - oldLessons.length;
      if (lessonsDiff != 0) changes['video_lessons'] = lessonsDiff;

      final oldVideos = oldContent['videos'] as List? ?? [];
      final newVideos = newContent['videos'] as List? ?? [];
      final videosDiff = newVideos.length - oldVideos.length;
      if (videosDiff != 0) changes['videos'] = videosDiff;

      if (changes.isNotEmpty) {
        await _notificationsService.createContentUpdateNotification(
          subjectId: subjectId,
          subjectName: 'مادة الفيديو', // يمكن تحسين هذا لاحقاً
          oldContent: oldContent,
          newContent: newContent,
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار تحديث الفيديو: $e');
    }
  }

  /// مزامنة جميع مواد الفيديوهات المشترك فيها
  Future<void> syncAllSubscribedVideoSubjects() async {
    try {
      final subscribedVideoSubjects = _subscriptionService
          .getAllowedVideoSubjectIds();
      debugPrint(
        '🔄 مزامنة ${subscribedVideoSubjects.length} مادة فيديو مشترك فيها...',
      );

      for (final subjectId in subscribedVideoSubjects) {
        await _fetchAndCacheVideoSubjectContent(subjectId);
      }

      debugPrint('✅ تم مزامنة جميع مواد الفيديوهات المشترك فيها');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة مواد الفيديوهات المشترك فيها: $e');
    }
  }

  /// الحصول على فيديوهات الدرس النشطة (فوري من الذاكرة)
  Future<List<Video>> getVideosByLesson(String lessonId) async {
    try {
      debugPrint('🔍 البحث عن فيديوهات للدرس: $lessonId');

      // البحث في جميع مواد الفيديوهات المحفوظة محلياً
      final allVideos = <Video>[];
      final subscribedVideoSubjects = _subscriptionService
          .getAllowedVideoSubjectIds();

      debugPrint('📋 المواد المشترك فيها: ${subscribedVideoSubjects.length}');
      for (var subjectId in subscribedVideoSubjects) {
        debugPrint('   - مادة: $subjectId');
      }

      for (final subjectId in subscribedVideoSubjects) {
        final content = await _loadLocalVideoSubjectContent(subjectId);
        final videos = content['videos'] as List<Video>;
        debugPrint('📹 مادة $subjectId تحتوي على ${videos.length} فيديو');

        final lessonVideos = videos
            .where((video) => video.lessonId == lessonId && video.isActive)
            .toList();
        debugPrint('   - منها ${lessonVideos.length} فيديو للدرس $lessonId');

        allVideos.addAll(lessonVideos);
      }

      debugPrint('🎯 إجمالي الفيديوهات الموجودة للدرس: ${allVideos.length}');

      // إذا لم توجد فيديوهات، جرب البحث المباشر في Firebase
      if (allVideos.isEmpty) {
        debugPrint('⚠️ لا توجد فيديوهات محلياً، البحث في Firebase...');

        final videosSnapshot = await _firestore
            .collection('videos')
            .where('lessonId', isEqualTo: lessonId)
            .where('isActive', isEqualTo: true)
            .orderBy('order')
            .get();

        final firebaseVideos = videosSnapshot.docs
            .map((doc) => Video.fromMap({...doc.data(), 'id': doc.id}))
            .toList();

        debugPrint(
          '📡 تم العثور على ${firebaseVideos.length} فيديو في Firebase',
        );

        return firebaseVideos;
      }

      return allVideos;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على فيديوهات الدرس: $e');
      return [];
    }
  }
}
