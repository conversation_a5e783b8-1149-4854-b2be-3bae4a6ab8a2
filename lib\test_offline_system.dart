import 'package:flutter/material.dart';
import 'shared/services/unified_offline_service.dart';

/// صفحة اختبار النظام الجديد Offline-First
/// يمكن الوصول إليها من التطبيق لاختبار جميع الوظائف
class TestOfflineSystemPage extends StatefulWidget {
  const TestOfflineSystemPage({super.key});

  @override
  State<TestOfflineSystemPage> createState() => _TestOfflineSystemPageState();
}

class _TestOfflineSystemPageState extends State<TestOfflineSystemPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  final List<String> _testResults = [];
  bool _isRunningTests = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار النظام الجديد'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // أزرار الاختبار
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isRunningTests ? null : _runQuickTest,
                        icon: const Icon(Icons.flash_on),
                        label: const Text('اختبار سريع'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isRunningTests ? null : _runFullTest,
                        icon: const Icon(Icons.assessment),
                        label: const Text('اختبار شامل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _showSystemStatus,
                        icon: const Icon(Icons.info),
                        label: const Text('حالة النظام'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _clearResults,
                        icon: const Icon(Icons.clear),
                        label: const Text('مسح النتائج'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // مؤشر التحميل
          if (_isRunningTests)
            const Padding(
              padding: EdgeInsets.all(16),
              child: LinearProgressIndicator(),
            ),
          
          // نتائج الاختبار
          Expanded(
            child: _testResults.isEmpty
                ? const Center(
                    child: Text(
                      'اضغط على أحد أزرار الاختبار لبدء الاختبار',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _testResults.length,
                    itemBuilder: (context, index) {
                      final result = _testResults[index];
                      final isSuccess = result.startsWith('✅');
                      final isError = result.startsWith('❌');
                      final isInfo = result.startsWith('ℹ️');
                      
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        color: isSuccess 
                            ? Colors.green.shade50
                            : isError 
                                ? Colors.red.shade50
                                : isInfo
                                    ? Colors.blue.shade50
                                    : null,
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            result,
                            style: TextStyle(
                              color: isSuccess 
                                  ? Colors.green.shade700
                                  : isError 
                                      ? Colors.red.shade700
                                      : isInfo
                                          ? Colors.blue.shade700
                                          : Colors.black,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// اختبار سريع للوظائف الأساسية
  Future<void> _runQuickTest() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });

    _addResult('🚀 بدء الاختبار السريع...');

    try {
      // اختبار التهيئة
      _addResult('ℹ️ اختبار حالة التهيئة...');
      final isInitialized = _offlineService.isInitialized;
      _addResult(isInitialized 
          ? '✅ النظام مهيأ بنجاح' 
          : '❌ النظام غير مهيأ');

      // اختبار تحميل الأقسام
      _addResult('ℹ️ اختبار تحميل الأقسام...');
      final stopwatch = Stopwatch()..start();
      final sections = await _offlineService.getActiveSections();
      stopwatch.stop();
      
      _addResult('✅ تم تحميل ${sections.length} قسم في ${stopwatch.elapsedMilliseconds}ms');
      
      if (stopwatch.elapsedMilliseconds > 100) {
        _addResult('⚠️ التحميل أبطأ من المتوقع (يجب أن يكون أقل من 100ms)');
      }

      // اختبار تحميل المواد
      if (sections.isNotEmpty) {
        _addResult('ℹ️ اختبار تحميل المواد...');
        final subjects = await _offlineService.getActiveSubjectsBySection(sections.first.id);
        _addResult('✅ تم تحميل ${subjects.length} مادة للقسم الأول');
      }

      // اختبار الاشتراكات
      _addResult('ℹ️ اختبار حالة الاشتراك...');
      final hasSubscription = _offlineService.hasActiveSubscription();
      _addResult('ℹ️ حالة الاشتراك: ${hasSubscription ? "نشط" : "غير نشط"}');

      // اختبار الإشعارات
      _addResult('ℹ️ اختبار الإشعارات...');
      final notifications = _offlineService.notifications;
      final unreadCount = _offlineService.unreadNotificationsCount;
      _addResult('✅ ${notifications.length} إشعار، ${unreadCount} غير مقروء');

      // اختبار حالة النظام
      _addResult('ℹ️ اختبار حالة النظام...');
      final systemStatus = _offlineService.systemStatus;
      _addResult('✅ حالة النظام: ${systemStatus.length} معلومة');

      _addResult('🎉 انتهى الاختبار السريع بنجاح!');

    } catch (e) {
      _addResult('❌ خطأ في الاختبار السريع: $e');
    } finally {
      setState(() => _isRunningTests = false);
    }
  }

  /// اختبار شامل لجميع الوظائف
  Future<void> _runFullTest() async {
    setState(() {
      _isRunningTests = true;
      _testResults.clear();
    });

    _addResult('🚀 بدء الاختبار الشامل...');

    try {
      // تشغيل الاختبار السريع أولاً
      await _runQuickTestInternal();

      // اختبار البحث
      _addResult('ℹ️ اختبار البحث...');
      final searchResults = await _offlineService.searchSections('test');
      _addResult('✅ البحث يعمل - ${searchResults.length} نتيجة');

      // اختبار المزامنة
      _addResult('ℹ️ اختبار المزامنة...');
      try {
        await _offlineService.manualSync();
        _addResult('✅ المزامنة تعمل');
      } catch (e) {
        _addResult('⚠️ المزامنة فشلت (قد يكون طبيعي بدون إنترنت): $e');
      }

      // اختبار الأداء
      _addResult('ℹ️ اختبار الأداء...');
      await _testPerformance();

      // اختبار الاستقرار
      _addResult('ℹ️ اختبار الاستقرار...');
      await _testStability();

      _addResult('🎉 انتهى الاختبار الشامل بنجاح!');

    } catch (e) {
      _addResult('❌ خطأ في الاختبار الشامل: $e');
    } finally {
      setState(() => _isRunningTests = false);
    }
  }

  /// اختبار سريع داخلي
  Future<void> _runQuickTestInternal() async {
    final sections = await _offlineService.getActiveSections();
    _addResult('✅ ${sections.length} قسم متاح');

    if (sections.isNotEmpty) {
      final subjects = await _offlineService.getActiveSubjectsBySection(sections.first.id);
      _addResult('✅ ${subjects.length} مادة في القسم الأول');
    }
  }

  /// اختبار الأداء
  Future<void> _testPerformance() async {
    final List<int> times = [];
    
    for (int i = 0; i < 5; i++) {
      final stopwatch = Stopwatch()..start();
      await _offlineService.getActiveSections();
      stopwatch.stop();
      times.add(stopwatch.elapsedMilliseconds);
    }
    
    final avgTime = times.reduce((a, b) => a + b) / times.length;
    _addResult('✅ متوسط وقت التحميل: ${avgTime.toStringAsFixed(1)}ms');
    
    if (avgTime > 100) {
      _addResult('⚠️ الأداء أبطأ من المتوقع');
    }
  }

  /// اختبار الاستقرار
  Future<void> _testStability() async {
    for (int i = 0; i < 10; i++) {
      final sections = await _offlineService.getActiveSections();
      if (sections.isEmpty) {
        _addResult('❌ فشل في المحاولة ${i + 1}');
        return;
      }
    }
    _addResult('✅ النظام مستقر بعد 10 محاولات');
  }

  /// عرض حالة النظام
  void _showSystemStatus() {
    final status = _offlineService.systemStatus;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة النظام'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: status.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text('${entry.key}:'),
                    ),
                    Text(
                      '${entry.value}',
                      style: TextStyle(
                        color: entry.value == true ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// مسح النتائج
  void _clearResults() {
    setState(() {
      _testResults.clear();
    });
  }

  /// إضافة نتيجة اختبار
  void _addResult(String result) {
    setState(() {
      _testResults.add(result);
    });
    
    // التمرير للأسفل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // يمكن إضافة تمرير تلقائي هنا إذا لزم الأمر
      }
    });
  }
}
