import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'shared/services/unified_offline_service.dart';

/// صفحة مراقبة أداء النظام الجديد
/// تعرض إحصائيات مفصلة عن الأداء والاستخدام
class PerformanceMonitorPage extends StatefulWidget {
  const PerformanceMonitorPage({super.key});

  @override
  State<PerformanceMonitorPage> createState() => _PerformanceMonitorPageState();
}

class _PerformanceMonitorPageState extends State<PerformanceMonitorPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  Timer? _refreshTimer;
  
  // إحصائيات الأداء
  int _sectionsCount = 0;
  int _subjectsCount = 0;
  int _notificationsCount = 0;
  int _unreadNotificationsCount = 0;
  double _lastLoadTime = 0.0;
  bool _isInitialized = false;
  Map<String, dynamic> _systemStatus = {};

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _startPeriodicRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// تحميل البيانات الأولية
  Future<void> _loadInitialData() async {
    await _refreshData();
  }

  /// بدء التحديث الدوري
  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        _refreshData();
      }
    });
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    try {
      // قياس وقت تحميل الأقسام
      final stopwatch = Stopwatch()..start();
      final sections = await _offlineService.getActiveSections();
      stopwatch.stop();

      // حساب عدد المواد
      int totalSubjects = 0;
      for (final section in sections) {
        final subjects = await _offlineService.getActiveSubjectsBySection(section.id);
        totalSubjects += subjects.length;
      }

      if (mounted) {
        setState(() {
          _sectionsCount = sections.length;
          _subjectsCount = totalSubjects;
          _notificationsCount = _offlineService.notifications.length;
          _unreadNotificationsCount = _offlineService.unreadNotificationsCount;
          _lastLoadTime = stopwatch.elapsedMilliseconds.toDouble();
          _isInitialized = _offlineService.isInitialized;
          _systemStatus = _offlineService.systemStatus;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحديث البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقب الأداء'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
          ),
        ],
      ),
      body: Consumer<UnifiedOfflineService>(
        builder: (context, service, child) {
          return RefreshIndicator(
            onRefresh: _refreshData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // بطاقة الحالة العامة
                  _buildStatusCard(),
                  const SizedBox(height: 16),
                  
                  // بطاقة الأداء
                  _buildPerformanceCard(),
                  const SizedBox(height: 16),
                  
                  // بطاقة البيانات
                  _buildDataCard(),
                  const SizedBox(height: 16),
                  
                  // بطاقة الإشعارات
                  _buildNotificationsCard(),
                  const SizedBox(height: 16),
                  
                  // بطاقة حالة النظام التفصيلية
                  _buildSystemStatusCard(),
                  const SizedBox(height: 16),
                  
                  // أزرار الإجراءات
                  _buildActionButtons(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      color: _isInitialized ? Colors.green.shade50 : Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isInitialized ? Icons.check_circle : Icons.error,
                  color: _isInitialized ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'حالة النظام',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _isInitialized ? Colors.green.shade700 : Colors.red.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isInitialized ? 'النظام يعمل بشكل طبيعي' : 'النظام غير مهيأ',
              style: TextStyle(
                color: _isInitialized ? Colors.green.shade600 : Colors.red.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceCard() {
    final isGoodPerformance = _lastLoadTime < 100;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.speed, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'الأداء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'وقت التحميل',
                    '${_lastLoadTime.toStringAsFixed(1)} ms',
                    isGoodPerformance ? Colors.green : Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'الهدف',
                    '< 100 ms',
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: (_lastLoadTime / 200).clamp(0.0, 1.0),
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                isGoodPerformance ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.data_usage, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'البيانات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'الأقسام',
                    '$_sectionsCount',
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'المواد',
                    '$_subjectsCount',
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.notifications, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'الإشعارات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'المجموع',
                    '$_notificationsCount',
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'غير مقروء',
                    '$_unreadNotificationsCount',
                    _unreadNotificationsCount > 0 ? Colors.red : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Colors.grey),
                SizedBox(width: 8),
                Text(
                  'حالة النظام التفصيلية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ..._systemStatus.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: Text('${entry.key}:'),
                    ),
                    Text(
                      '${entry.value}',
                      style: TextStyle(
                        color: entry.value == true ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إجراءات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      try {
                        await _offlineService.manualSync();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث البيانات'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('خطأ في التحديث: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.sync),
                    label: const Text('مزامنة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      await _offlineService.forceRefreshAll();
                      await _refreshData();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إعادة تحميل جميع البيانات'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تحميل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
