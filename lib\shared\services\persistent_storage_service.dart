import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../models/video_section_model.dart';
import '../../core/models/section.dart';
import '../models/subject_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';

/// خدمة التخزين الدائم للبيانات المحلية - لا تُحذف أبداً
class PersistentStorageService {
  static final PersistentStorageService _instance =
      PersistentStorageService._internal();
  static PersistentStorageService get instance => _instance;
  PersistentStorageService._internal();

  late Directory _storageDirectory;
  bool _isInitialized = false;

  /// الحصول على مجلد التخزين
  Directory get storageDirectory => _storageDirectory;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final appDir = await getApplicationDocumentsDirectory();
      _storageDirectory = Directory('${appDir.path}/persistent_data');

      if (!await _storageDirectory.exists()) {
        await _storageDirectory.create(recursive: true);
      }

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة التخزين الدائم: ${_storageDirectory.path}');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة التخزين الدائم: $e');
    }
  }

  /// حفظ أقسام الفيديوهات
  Future<void> saveVideoSections(List<VideoSection> sections) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_sections.json');
      final data = sections.map((s) => s.toLocalMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${sections.length} قسم فيديو دائمياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ أقسام الفيديوهات: $e');
    }
  }

  /// تحميل أقسام الفيديوهات
  Future<List<VideoSection>> loadVideoSections() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_sections.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final sections = data
          .map((item) => VideoSection.fromLocalMap(item))
          .toList();

      debugPrint('📱 تم تحميل ${sections.length} قسم فيديو من التخزين الدائم');
      return sections;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أقسام الفيديوهات: $e');
      return [];
    }
  }

  /// حفظ مواد الفيديوهات
  Future<void> saveVideoSubjects(List<VideoSubject> subjects) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_subjects.json');
      final data = subjects.map((s) => s.toLocalMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${subjects.length} مادة فيديو دائمياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ مواد الفيديوهات: $e');
    }
  }

  /// تحميل مواد الفيديوهات
  Future<List<VideoSubject>> loadVideoSubjects() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_subjects.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final subjects = data
          .map((item) => VideoSubject.fromLocalMap(item))
          .toList();

      debugPrint('📱 تم تحميل ${subjects.length} مادة فيديو من التخزين الدائم');
      return subjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد الفيديوهات: $e');
      return [];
    }
  }

  /// حفظ وحدات الفيديوهات
  Future<void> saveVideoUnits(List<VideoUnit> units) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_units.json');
      final data = units.map((u) => u.toLocalMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${units.length} وحدة فيديو دائمياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ وحدات الفيديوهات: $e');
    }
  }

  /// تحميل وحدات الفيديوهات
  Future<List<VideoUnit>> loadVideoUnits() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_units.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final units = data.map((item) => VideoUnit.fromLocalMap(item)).toList();

      debugPrint('📱 تم تحميل ${units.length} وحدة فيديو من التخزين الدائم');
      return units;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل وحدات الفيديوهات: $e');
      return [];
    }
  }

  /// حفظ دروس الفيديوهات
  Future<void> saveVideoLessons(List<VideoLesson> lessons) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_lessons.json');
      final data = lessons.map((l) => l.toLocalMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${lessons.length} درس فيديو دائمياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ دروس الفيديوهات: $e');
    }
  }

  /// تحميل دروس الفيديوهات
  Future<List<VideoLesson>> loadVideoLessons() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/video_lessons.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final lessons = data
          .map((item) => VideoLesson.fromLocalMap(item))
          .toList();

      debugPrint('📱 تم تحميل ${lessons.length} درس فيديو من التخزين الدائم');
      return lessons;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل دروس الفيديوهات: $e');
      return [];
    }
  }

  /// حفظ الفيديوهات
  Future<void> saveVideos(List<Video> videos) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/videos.json');
      final data = videos.map((v) => v.toLocalMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${videos.length} فيديو دائمياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الفيديوهات: $e');
    }
  }

  /// تحميل الفيديوهات
  Future<List<Video>> loadVideos() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/videos.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final videos = data.map((item) => Video.fromLocalMap(item)).toList();

      debugPrint('📱 تم تحميل ${videos.length} فيديو من التخزين الدائم');
      return videos;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الفيديوهات: $e');
      return [];
    }
  }

  /// حفظ آخر تحديث
  Future<void> saveLastUpdate(String key) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/last_updates.json');
      Map<String, dynamic> updates = {};

      if (await file.exists()) {
        final content = await file.readAsString();
        updates = jsonDecode(content);
      }

      updates[key] = DateTime.now().toIso8601String();
      await file.writeAsString(jsonEncode(updates));

      debugPrint('📅 تم حفظ آخر تحديث لـ $key');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ آخر تحديث: $e');
    }
  }

  /// تحميل آخر تحديث
  Future<DateTime?> getLastUpdate(String key) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/last_updates.json');
      if (!await file.exists()) return null;

      final content = await file.readAsString();
      final Map<String, dynamic> updates = jsonDecode(content);

      if (updates.containsKey(key)) {
        return DateTime.parse(updates[key]);
      }

      return null;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل آخر تحديث: $e');
      return null;
    }
  }

  /// التحقق من الحاجة للتحديث (كل 6 ساعات)
  Future<bool> needsUpdate(String key) async {
    final lastUpdate = await getLastUpdate(key);
    if (lastUpdate == null) return true;

    final now = DateTime.now();
    final difference = now.difference(lastUpdate);

    return difference.inHours >= 6; // تحديث كل 6 ساعات
  }

  /// حفظ وقت آخر تحديث لمفتاح معين
  Future<void> setLastUpdate(String key, DateTime timestamp) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/last_updates.json');
      Map<String, dynamic> updates = {};

      if (await file.exists()) {
        final content = await file.readAsString();
        updates = jsonDecode(content);
      }

      updates[key] = timestamp.toIso8601String();
      await file.writeAsString(jsonEncode(updates));

      debugPrint('✅ تم حفظ وقت التحديث للمفتاح: $key');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ وقت التحديث: $e');
    }
  }

  /// التأكد من التهيئة
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// الحصول على حجم البيانات المحفوظة
  Future<int> getStorageSize() async {
    await _ensureInitialized();
    try {
      int totalSize = 0;
      final files = _storageDirectory.listSync();

      for (final file in files) {
        if (file is File) {
          totalSize += await file.length();
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('❌ خطأ في حساب حجم التخزين: $e');
      return 0;
    }
  }

  /// حفظ أقسام الاختبارات
  Future<void> saveSections(List<Section> sections) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/sections.json');
      final data = sections.map((s) => s.toLocalMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${sections.length} قسم في التخزين الدائم');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ أقسام الاختبارات: $e');
    }
  }

  /// تحميل أقسام الاختبارات
  Future<List<Section>> loadSections() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/sections.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final sections = data
          .map((json) => Section.fromLocalMap(json as Map<String, dynamic>))
          .toList();

      debugPrint('📱 تم تحميل ${sections.length} قسم من التخزين الدائم');
      return sections;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أقسام الاختبارات: $e');
      return [];
    }
  }

  /// حفظ مواد الاختبارات
  Future<void> saveSubjects(List<Subject> subjects) async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/subjects.json');
      final data = subjects.map((s) => s.toMap()).toList();
      await file.writeAsString(jsonEncode(data));
      debugPrint('💾 تم حفظ ${subjects.length} مادة اختبار دائمياً');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ مواد الاختبارات: $e');
    }
  }

  /// تحميل مواد الاختبارات
  Future<List<Subject>> loadSubjects() async {
    await _ensureInitialized();
    try {
      final file = File('${_storageDirectory.path}/subjects.json');
      if (!await file.exists()) return [];

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final subjects = data.map((item) => Subject.fromMap(item)).toList();

      debugPrint(
        '📱 تم تحميل ${subjects.length} مادة اختبار من التخزين الدائم',
      );
      return subjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد الاختبارات: $e');
      return [];
    }
  }

  /// تنسيق حجم البيانات
  String formatStorageSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}
