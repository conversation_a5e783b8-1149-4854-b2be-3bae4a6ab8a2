# نظام Offline-First الجديد لتطبيق Smart Edu

## نظرة عامة

تم تطوير نظام جديد متكامل يطبق مبدأ **Offline-First** لتقليل عدد قراءات Firebase إلى أقل حد ممكن، مع ضمان عمل التطبيق بسلاسة حتى بدون اتصال بالإنترنت.

## الهدف الرئيسي

تقليل عدد قراءات Firebase اليومية من آلاف القراءات إلى أقل من 50,000 قراءة يومياً (الحد المجاني) من خلال:

1. **النظام الأول**: تحميل الأقسام والمواد دفعة واحدة وعرضها للجميع
2. **النظام الثاني**: تحميل المحتوى التفصيلي فقط للمواد المشترك فيها
3. **المزامنة الذكية**: تحديث البيانات فقط عند وجود تغييرات حقيقية

## المكونات الرئيسية

### 1. Repository Pattern الموحد

#### BaseRepository
- نمط أساسي يطبق Offline-First لجميع أنواع البيانات
- يجمع بين التخزين المحلي والبعيد
- يعطي الأولوية للبيانات المحلية

#### Repositories المتخصصة
- `SectionsRepository`: إدارة الأقسام
- `SubjectsRepository`: إدارة المواد
- `SubjectContentRepository`: إدارة محتوى المواد (وحدات، دروس، أسئلة)
- `VideoSectionsRepository`: إدارة أقسام الفيديوهات
- `VideoSubjectsRepository`: إدارة مواد الفيديوهات
- `VideoContentRepository`: إدارة محتوى الفيديوهات

### 2. خدمات النظام

#### OfflineFirstService
الخدمة الأساسية التي تنسق بين جميع المكونات:
```dart
// تهيئة النظام
await OfflineFirstService.instance.initialize();

// الحصول على البيانات فوراً من الذاكرة
final sections = await OfflineFirstService.instance.getActiveSections();
```

#### UnifiedOfflineService
النقطة الوحيدة للوصول لجميع البيانات:
```dart
// تهيئة النظام الكامل
await UnifiedOfflineService.instance.initialize();

// الحصول على البيانات
final subjects = await UnifiedOfflineService.instance.getActiveSubjectsBySection(sectionId);
```

#### LocalNotificationsService
إدارة الإشعارات المحلية عند تحديث المحتوى:
```dart
// إنشاء إشعار تحديث
await LocalNotificationsService.instance.createContentUpdateNotification(
  subjectId: 'math',
  subjectName: 'الرياضيات',
  oldContent: oldData,
  newContent: newData,
);
```

#### BackgroundSyncService
المزامنة في الخلفية حتى عند إغلاق التطبيق:
```dart
// بدء المزامنة في الخلفية
await BackgroundSyncService.instance.initialize();

// مزامنة فورية
await BackgroundSyncService.instance.syncNow();
```

#### FCMService
استقبال إشعارات Firebase وتحفيز المزامنة:
```dart
// تهيئة FCM
await FCMService.instance.initialize();

// الاشتراك في موضوع
await FCMService.instance.subscribeToTopic('content_updates');
```

## كيفية الاستخدام

### 1. تهيئة النظام في main.dart

```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Firebase
  await Firebase.initializeApp();
  
  // تهيئة النظام الموحد
  await UnifiedOfflineService.instance.initialize();
  
  runApp(MyApp());
}
```

### 2. استخدام النظام في الواجهات

```dart
class SectionsPage extends StatefulWidget {
  @override
  State<SectionsPage> createState() => _SectionsPageState();
}

class _SectionsPageState extends State<SectionsPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  List<Section> _sections = [];

  @override
  void initState() {
    super.initState();
    _loadSectionsImmediately();
  }

  /// تحميل فوري بدون انتظار
  Future<void> _loadSectionsImmediately() async {
    final sections = await _offlineService.getActiveSections();
    setState(() {
      _sections = sections.cast<Section>();
    });
  }

  /// تحديث يدوي
  Future<void> _refreshSections() async {
    await _offlineService.manualSync();
    await _loadSectionsImmediately();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshSections,
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    // عرض البيانات فوراً بدون مؤشرات تحميل
    if (_sections.isEmpty) {
      return _buildEmptyState();
    }
    return _buildSectionsList();
  }
}
```

### 3. مراقبة الإشعارات

```dart
class NotificationsWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<UnifiedOfflineService>(
      builder: (context, service, child) {
        final unreadCount = service.unreadNotificationsCount;
        return Badge(
          count: unreadCount,
          child: IconButton(
            icon: Icon(Icons.notifications),
            onPressed: () => _showNotifications(context, service),
          ),
        );
      },
    );
  }
}
```

## المزايا الرئيسية

### 1. أداء فائق
- **عرض فوري**: البيانات تظهر فوراً من الذاكرة المحلية
- **لا توجد مؤشرات تحميل**: المستخدم لا ينتظر أبداً
- **عمل بدون إنترنت**: التطبيق يعمل حتى بدون اتصال

### 2. توفير في التكاليف
- **تقليل القراءات**: من آلاف إلى مئات القراءات يومياً
- **مزامنة ذكية**: تحديث فقط عند وجود تغييرات
- **تحميل انتقائي**: محتوى المواد المشترك فيها فقط

### 3. تجربة مستخدم محسنة
- **إشعارات ذكية**: تنبيه المستخدم عند تحديث المحتوى
- **مزامنة خلفية**: التحديثات تحدث تلقائياً
- **استقرار عالي**: لا توجد أخطاء شبكة مرئية للمستخدم

## التحديثات المطلوبة

### 1. إضافة Dependencies

```yaml
dependencies:
  workmanager: ^0.5.2
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.2
```

### 2. تحديث Firebase Rules

```javascript
// قواعد Firestore الجديدة
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // البيانات الوصفية للتحديثات
    match /sections_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /subjects_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /subject_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
  }
}
```

### 3. إعداد FCM

```json
// في android/app/src/main/AndroidManifest.xml
<service
    android:name=".MyFirebaseMessagingService"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
    </intent-filter>
</service>
```

## الخطوات التالية

1. **اختبار النظام**: تشغيل التطبيق والتأكد من عمل جميع المكونات
2. **مراقبة الأداء**: قياس عدد قراءات Firebase الفعلية
3. **تحسين إضافي**: إضافة المزيد من الذكاء للمزامنة
4. **توثيق شامل**: إنشاء دليل مطور مفصل

## ملاحظات مهمة

- **التوافق**: النظام متوافق مع الكود الحالي
- **التدرج**: يمكن تطبيق النظام تدريجياً
- **المرونة**: سهولة إضافة مكونات جديدة
- **الصيانة**: كود منظم وقابل للصيانة

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة:
- ملفات الـ logs في التطبيق
- إحصائيات Firebase Console
- تقارير الأداء في التطبيق
