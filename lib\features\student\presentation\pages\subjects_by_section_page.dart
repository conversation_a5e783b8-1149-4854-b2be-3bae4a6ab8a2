import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/section.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/persistent_storage_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/unified_offline_service.dart';
import '../../../../shared/services/single_read_data_service.dart';

import 'subject_detail_page.dart';

class SubjectsBySectionPage extends StatefulWidget {
  final Section section;
  final bool isFreeAccess; // هل الوصول مجاني (للأقسام المجانية)

  const SubjectsBySectionPage({
    super.key,
    required this.section,
    this.isFreeAccess = false,
  });

  @override
  State<SubjectsBySectionPage> createState() => _SubjectsBySectionPageState();
}

class _SubjectsBySectionPageState extends State<SubjectsBySectionPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  List<Subject> _subjects = [];
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    debugPrint('🔥 بدء تحميل مواد القسم بالنظام الجديد...');
    _loadSubjectsImmediately();

    // الاستماع لتغييرات SubscriptionService
    SubscriptionService.instance.addListener(_onSubscriptionChanged);
  }

  /// تحميل فوري لمواد القسم من النظام الجديد
  Future<void> _loadSubjectsImmediately() async {
    try {
      // استخدام SingleReadDataService بدلاً من الخدمة الوهمية
      final dataService = SingleReadDataService.instance;
      final subjects = dataService.getSubjectsBySection(widget.section.id);

      if (mounted) {
        setState(() {
          _subjects = subjects;
        });
        debugPrint(
          '⚡ تم تحميل ${_subjects.length} مادة فوراً من SingleReadDataService للقسم ${widget.section.name}',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد القسم: $e');
      // استخدام النظام القديم كـ fallback
      await _loadDataImmediately();
    }
  }

  /// تحميل البيانات فوراً من الذاكرة الدائمة (النظام القديم - fallback)
  Future<void> _loadDataImmediately() async {
    try {
      // جلب البيانات مباشرة من الذاكرة الدائمة (بدون أي اعتماد على الشبكة)
      final persistentSubjects = await PersistentStorageService.instance
          .loadSubjects();

      debugPrint(
        '🔍 جميع المواد في الذاكرة الدائمة (${persistentSubjects.length}):',
      );
      for (var subject in persistentSubjects) {
        debugPrint(
          '   - ${subject.name} (sectionId: "${subject.sectionId}", isActive: ${subject.isActive})',
        );
      }

      // تصفية المواد حسب القسم والحالة النشطة
      final subjects = persistentSubjects
          .where((s) => s.isActive && s.sectionId == widget.section.id)
          .toList();

      debugPrint(
        '🔍 المواد للقسم ${widget.section.id}: ${subjects.map((s) => s.name).join(', ')}',
      );

      // عرض البيانات فوراً
      setState(() {
        _subjects = subjects;
      });

      debugPrint('🚀 تم عرض ${subjects.length} مادة فوراً من الذاكرة الدائمة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد من الذاكرة الدائمة: $e');
      // في حالة الخطأ، استخدم قائمة فارغة
      setState(() {
        _subjects = [];
      });
    }
  }

  @override
  void dispose() {
    SubscriptionService.instance.removeListener(_onSubscriptionChanged);
    super.dispose();
  }

  void _onSubscriptionChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          widget.section.name,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // زر تحديث جميع المواد
          IconButton(
            icon: Icon(Icons.refresh, color: Colors.white),
            onPressed: _isRefreshing ? null : _refreshSubjects,
            tooltip: 'تحديث جميع المواد',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة مع إمكانية التحديث
    return RefreshIndicator(
      onRefresh: _refreshSubjects,
      child: _subjects.isNotEmpty
          ? _buildSubjectsContent()
          : _buildEmptyState(),
    );
  }

  /// تحديث يدوي مع مزامنة (النظام الجديد)
  Future<void> _refreshSubjects() async {
    if (_isRefreshing) return;

    setState(() => _isRefreshing = true);

    try {
      await _offlineService.manualSync();
      await _loadSubjectsImmediately();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المواد: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  Widget _buildSubjectsContent() {
    return GridView.builder(
      padding: EdgeInsets.all(16.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: AdaptiveSizing.instance.adaptiveGridColumns(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4, // 4 أعمدة على Windows
        ),
        crossAxisSpacing: 16.w,
        mainAxisSpacing: 16.h,
        childAspectRatio: AdaptiveSizing.instance.adaptiveCardAspectRatio(
          mobileRatio: 0.85,
          tabletRatio: 0.9,
          desktopRatio: 1.0, // نسبة أفضل للشاشات الكبيرة
        ),
      ),
      itemCount: _subjects.length,
      itemBuilder: (context, index) {
        final subject = _subjects[index];
        return _buildSubjectCard(subject);
      },
    );
  }

  Widget _buildSubjectCard(Subject subject) {
    final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
      subject.id,
    );
    // إذا كان القسم مجاني أو المادة مجانية، فالوصول متاح
    final isFreeAccess = widget.isFreeAccess || subject.isFree;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.r),
          onTap: () => _navigateToSubject(subject, isFreeAccess),
          child: Padding(
            padding: EdgeInsets.all(20.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة المادة
                Container(
                  width: 60.w,
                  height: 60.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(
                          int.parse(subject.color.replaceFirst('#', '0xFF')),
                        ),
                        Color(
                          int.parse(subject.color.replaceFirst('#', '0xFF')),
                        ).withValues(alpha: 0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(50.r),
                  ),
                  child: Icon(Icons.book, color: Colors.white, size: 30.w),
                ),

                SizedBox(height: 16.h),

                // اسم المادة
                Text(
                  subject.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: 12.h),

                // حالة الاشتراك
                if (isFreeAccess)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'متاح',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                else if (isSubscribed)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'متاح',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.green,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                else
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'مقفل',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.orange,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.book_outlined, size: 80.w, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد في هذا القسم',
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة المواد قريباً',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  void _navigateToSubject(Subject subject, bool isFreeAccess) {
    final isSubscribed = SubscriptionService.instance.isSubscribedToSubject(
      subject.id,
    );

    // التحقق من إمكانية الوصول
    // إذا كان القسم مجاني أو المادة مجانية، فالوصول متاح
    final hasAccess = widget.isFreeAccess || subject.isFree || isSubscribed;

    if (!hasAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تفعيل الاشتراك للوصول إلى هذه المادة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectDetailPage(
          subject: subject,
          isFreeAccess: widget.isFreeAccess || subject.isFree,
        ),
      ),
    );
  }
}
