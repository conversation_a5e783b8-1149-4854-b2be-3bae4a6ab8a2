import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/flavor.dart';
import 'shared/services/single_read_data_service.dart';
import 'shared/services/new_subscription_service.dart';
import 'shared/services/simple_video_service.dart';
import 'features/student/presentation/pages/simple_student_home_page.dart';

/// تطبيق مبسط يستخدم النظام الجديد فقط
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  // تهيئة الخدمات الجديدة
  await _initializeServices();
  
  runApp(const SimpleSmartEduApp());
}

/// تهيئة الخدمات الجديدة فقط
Future<void> _initializeServices() async {
  try {
    debugPrint('🚀 بدء تهيئة النظام الجديد...');
    
    // تهيئة خدمة القراءة الواحدة
    await SingleReadDataService.instance.initialize();
    debugPrint('✅ تم تهيئة خدمة القراءة الواحدة');
    
    // تهيئة خدمة الفيديوهات المبسطة
    await SimpleVideoService.instance.initialize();
    debugPrint('✅ تم تهيئة خدمة الفيديوهات المبسطة');
    
    debugPrint('🎉 تم تهيئة النظام الجديد بنجاح!');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة النظام الجديد: $e');
  }
}

class SimpleSmartEduApp extends StatelessWidget {
  const SimpleSmartEduApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            // النظام الجديد للقراءة الواحدة
            ChangeNotifierProvider.value(value: SingleReadDataService.instance),
            ChangeNotifierProvider.value(value: SubscriptionService.instance),
            ChangeNotifierProvider.value(value: SimpleVideoService.instance),
          ],
          child: MaterialApp(
            title: 'Smart Edu - النظام الجديد',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            home: const SimpleStudentHomePage(),
          ),
        );
      },
    );
  }
}

/// صفحة عرض حالة النظام الجديد
class SystemStatusPage extends StatelessWidget {
  const SystemStatusPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حالة النظام الجديد'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<SingleReadDataService>(
        builder: (context, dataService, child) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات النظام
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '📊 إحصائيات النظام الجديد',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildStatRow('الأقسام', dataService.sections.length),
                        _buildStatRow('المواد', dataService.subjects.length),
                        _buildStatRow('أقسام الفيديو', dataService.videoSections.length),
                        _buildStatRow('مواد الفيديو', dataService.videoSubjects.length),
                        _buildStatRow('الوحدات', dataService.units.length),
                        _buildStatRow('الدروس', dataService.lessons.length),
                        _buildStatRow('الأسئلة', dataService.questions.length),
                        _buildStatRow('وحدات الفيديو', dataService.videoUnits.length),
                        _buildStatRow('دروس الفيديو', dataService.videoLessons.length),
                        _buildStatRow('الفيديوهات', dataService.videos.length),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // معلومات آخر تحديث
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '🕒 معلومات التحديث',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          dataService.lastUpdateTime != null
                              ? 'آخر تحديث: ${_formatDateTime(dataService.lastUpdateTime!)}'
                              : 'لم يتم التحديث بعد',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // زر التحديث اليدوي
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: dataService.isLoading 
                        ? null 
                        : () => _performManualUpdate(context),
                    icon: dataService.isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.refresh),
                    label: Text(
                      dataService.isLoading ? 'جاري التحديث...' : 'تحديث البيانات',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                  ),
                ),
                
                if (dataService.isLoading) ...[
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: dataService.progress,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${(dataService.progress * 100).toInt()}% - ${dataService.currentStep}',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatRow(String label, int count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _performManualUpdate(BuildContext context) async {
    final dataService = context.read<SingleReadDataService>();
    final success = await dataService.performSingleRead();
    
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'تم تحديث البيانات بنجاح' : 'فشل في تحديث البيانات',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}
