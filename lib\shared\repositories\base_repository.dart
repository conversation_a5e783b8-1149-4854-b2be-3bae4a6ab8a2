import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../services/persistent_storage_service.dart';

/// Repository أساسي يطبق نمط Offline-First
/// يجمع بين التخزين المحلي والبعيد مع إعطاء الأولوية للبيانات المحلية
abstract class BaseRepository<T> extends ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;

  // Getters للوصول للخدمات من الفئات الفرعية
  PersistentStorageService get persistentStorage => _persistentStorage;
  FirebaseFirestore get firestore => _firestore;

  /// اسم المجموعة في Firebase
  String get collectionName;

  /// مفتاح التخزين المحلي
  String get storageKey;

  /// تحويل البيانات من Firebase إلى النموذج
  T fromFirestore(Map<String, dynamic> data, String documentId);

  /// تحويل النموذج إلى خريطة للتخزين المحلي
  Map<String, dynamic> toLocalMap(T item);

  /// تحويل الخريطة المحلية إلى النموذج
  T fromLocalMap(Map<String, dynamic> map);

  /// تحويل النموذج إلى خريطة Firebase
  Map<String, dynamic> toFirestore(T item);

  /// الحصول على البيانات المحلية فوراً (Offline-First)
  Future<List<T>> getLocalData() async {
    try {
      await _persistentStorage.initialize();
      final data = await loadFromLocalStorage();
      debugPrint(
        '⚡ تم تحميل ${data.length} عنصر من التخزين المحلي للمفتاح: $storageKey',
      );
      return data;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحلية: $e');
      return [];
    }
  }

  /// تحميل البيانات من التخزين المحلي
  Future<List<T>> loadFromLocalStorage() async {
    // تنفيذ افتراضي مؤقت - يجب تنفيذها في كل Repository فرعي
    debugPrint(
      '⚠️ استخدام التنفيذ الافتراضي لـ loadFromLocalStorage في $storageKey',
    );
    debugPrint('⚠️ Repository Type: ${runtimeType.toString()}');
    return [];
  }

  /// حفظ البيانات في التخزين المحلي
  Future<void> saveToLocalStorage(List<T> data) async {
    // تنفيذ افتراضي مؤقت - يجب تنفيذها في كل Repository فرعي
    debugPrint(
      '⚠️ استخدام التنفيذ الافتراضي لـ saveToLocalStorage في $storageKey',
    );
    debugPrint('⚠️ Repository Type: ${runtimeType.toString()}');
    // لا نفعل شيئاً في التنفيذ الافتراضي
  }

  /// التحقق من وجود تحديثات في Firebase
  Future<bool> hasRemoteUpdates() async {
    try {
      final lastLocalUpdate = await _persistentStorage.getLastUpdate(
        storageKey,
      );
      if (lastLocalUpdate == null) return true;

      // التحقق من آخر تحديث في Firebase
      final metadataDoc = await _firestore
          .collection('${collectionName}_metadata')
          .doc('last_update')
          .get();

      if (!metadataDoc.exists) {
        debugPrint('❌ لا توجد بيانات وصفية للمجموعة: $collectionName');
        return false;
      }

      final remoteLastUpdate = (metadataDoc.data()!['timestamp'] as Timestamp)
          .toDate();
      final hasUpdates = remoteLastUpdate.isAfter(lastLocalUpdate);

      debugPrint('🔍 التحقق من التحديثات للمجموعة: $collectionName');
      debugPrint('📅 آخر تحديث محلي: $lastLocalUpdate');
      debugPrint('📅 آخر تحديث بعيد: $remoteLastUpdate');
      debugPrint('🔄 يوجد تحديثات: $hasUpdates');

      return hasUpdates;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التحديثات البعيدة: $e');
      return false;
    }
  }

  /// جلب البيانات من Firebase وحفظها محلياً
  Future<List<T>> fetchAndCacheRemoteData() async {
    try {
      debugPrint('🔄 جلب البيانات من Firebase للمجموعة: $collectionName');

      final querySnapshot = await _firestore.collection(collectionName).get();

      final data = querySnapshot.docs
          .map((doc) => fromFirestore(doc.data(), doc.id))
          .toList();

      // حفظ البيانات محلياً
      await saveToLocalStorage(data);

      // تحديث وقت آخر مزامنة
      await _persistentStorage.setLastUpdate(storageKey, DateTime.now());

      debugPrint('✅ تم جلب وحفظ ${data.length} عنصر للمجموعة: $collectionName');
      notifyListeners();

      return data;
    } catch (e) {
      debugPrint('❌ خطأ في جلب البيانات البعيدة: $e');
      rethrow;
    }
  }

  /// تحديث البيانات المحلية إذا كانت هناك تحديثات بعيدة
  Future<List<T>> syncIfNeeded() async {
    try {
      // أولاً، إرجاع البيانات المحلية فوراً
      final localData = await getLocalData();

      // ثم التحقق من التحديثات في الخلفية
      if (await hasRemoteUpdates()) {
        debugPrint('🔄 يوجد تحديثات جديدة، بدء المزامنة...');
        return await fetchAndCacheRemoteData();
      } else {
        debugPrint('✅ البيانات المحلية محدثة للمجموعة: $collectionName');
        return localData;
      }
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة، إرجاع البيانات المحلية: $e');
      return await getLocalData();
    }
  }

  /// إضافة عنصر جديد (للاستخدام في تطبيق الإدارة)
  Future<String> addItem(T item) async {
    try {
      final docRef = _firestore.collection(collectionName).doc();
      await docRef.set(toFirestore(item));

      // تحديث البيانات الوصفية
      await _updateMetadata();

      debugPrint('✅ تم إضافة عنصر جديد: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة العنصر: $e');
      rethrow;
    }
  }

  /// تحديث عنصر (للاستخدام في تطبيق الإدارة)
  Future<void> updateItem(String id, T item) async {
    try {
      await _firestore
          .collection(collectionName)
          .doc(id)
          .update(toFirestore(item));

      // تحديث البيانات الوصفية
      await _updateMetadata();

      debugPrint('✅ تم تحديث العنصر: $id');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث العنصر: $e');
      rethrow;
    }
  }

  /// حذف عنصر (للاستخدام في تطبيق الإدارة)
  Future<void> deleteItem(String id) async {
    try {
      await _firestore.collection(collectionName).doc(id).delete();

      // تحديث البيانات الوصفية
      await _updateMetadata();

      debugPrint('✅ تم حذف العنصر: $id');
    } catch (e) {
      debugPrint('❌ خطأ في حذف العنصر: $e');
      rethrow;
    }
  }

  /// تحديث البيانات الوصفية (للاستخدام في تطبيق الإدارة)
  Future<void> _updateMetadata() async {
    try {
      await _firestore
          .collection('${collectionName}_metadata')
          .doc('last_update')
          .set({
            'timestamp': FieldValue.serverTimestamp(),
            'collection': collectionName,
          }, SetOptions(merge: true));

      debugPrint('✅ تم تحديث البيانات الوصفية للمجموعة: $collectionName');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات الوصفية: $e');
    }
  }

  /// تنظيف البيانات المحلية
  Future<void> clearLocalData() async {
    try {
      await saveToLocalStorage([]);
      debugPrint('✅ تم تنظيف البيانات المحلية للمفتاح: $storageKey');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات المحلية: $e');
    }
  }

  /// فرض إعادة التحميل من Firebase
  Future<List<T>> forceRefresh() async {
    try {
      debugPrint('🔄 فرض إعادة التحميل من Firebase للمجموعة: $collectionName');
      return await fetchAndCacheRemoteData();
    } catch (e) {
      debugPrint('❌ خطأ في فرض إعادة التحميل: $e');
      return await getLocalData();
    }
  }
}
