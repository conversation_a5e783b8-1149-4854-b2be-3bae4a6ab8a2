import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../core/widgets/adaptive_widgets.dart';
import '../../../../shared/models/video_section_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/unified_offline_service.dart';

import 'video_subjects_page.dart';
import 'free_video_sections_page.dart';

/// صفحة أقسام الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoSectionsPage extends StatefulWidget {
  const VideoSectionsPage({super.key});

  @override
  State<VideoSectionsPage> createState() => _VideoSectionsPageState();
}

class _VideoSectionsPageState extends State<VideoSectionsPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoSection> _sections = [];
  bool _hasSubscription = false;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadVideoSectionsImmediately();
    _checkSubscription();
  }

  /// تحميل فوري لأقسام الفيديو من النظام الجديد
  Future<void> _loadVideoSectionsImmediately() async {
    try {
      final sections = await _offlineService.getActiveVideoSections();
      if (mounted) {
        setState(() {
          _sections = sections.cast<VideoSection>();
        });
        debugPrint(
          '⚡ تم تحميل ${_sections.length} قسم فيديو فوراً بالنظام الجديد',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أقسام الفيديو: $e');
    }
  }

  /// فحص حالة الاشتراك
  Future<void> _checkSubscription() async {
    try {
      final hasSubscription = await _subscriptionService
          .hasActiveSubscription();
      if (mounted) {
        setState(() {
          _hasSubscription = hasSubscription;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص الاشتراك: $e');
    }
  }

  /// تحديث يدوي مع مزامنة (النظام الجديد)
  Future<void> _refreshVideoSections() async {
    if (_isRefreshing) return;

    setState(() => _isRefreshing = true);

    try {
      await _offlineService.manualSync();
      await _loadVideoSectionsImmediately();
      await _checkSubscription();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث أقسام الفيديو: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AdaptiveAppBar(
      title: 'الفيديوهات',
      gradient: AppTheme.primaryGradient,
      actions: [
        if (_isRefreshing)
          Padding(
            padding: EdgeInsets.only(left: 8.adaptiveSpacing),
            child: SizedBox(
              width: 16.adaptiveIcon,
              height: 16.adaptiveIcon,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
          ),
        // زر تجربة التطبيق مع شعار الفيديو
        Container(
          margin: EdgeInsets.only(left: 8.adaptiveSpacing),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20.adaptiveRadius),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const FreeVideoSectionsPage(),
                  ),
                );
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.adaptiveSpacing,
                  vertical: 6.adaptiveSpacing,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20.adaptiveRadius),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AdaptiveIcon(
                      Icons.play_circle_outline,
                      size: 20,
                      color: Colors.white,
                    ),
                    SizedBox(width: 4.adaptiveSpacing),
                    AdaptiveText(
                      'تجربة التطبيق',
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة مع إمكانية التحديث
    return RefreshIndicator(
      onRefresh: _refreshVideoSections,
      child: _sections.isNotEmpty
          ? _buildSectionsContent()
          : _buildEmptyState(),
    );
  }

  Widget _buildSectionsContent() {
    return Padding(
      padding: EdgeInsets.all(16.adaptiveSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20.adaptiveSpacing),
          Expanded(
            child: AdaptiveGridView(
              shrinkWrap: false,
              children: _sections
                  .map((section) => _buildSectionCard(section))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.adaptiveSpacing),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.adaptiveSpacing),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.adaptiveRadius),
            ),
            child: AdaptiveIcon(
              Icons.video_library,
              size: 24,
              color: Colors.white,
            ),
          ),
          SizedBox(width: 16.adaptiveSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AdaptiveText(
                  'أقسام الفيديوهات',
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                SizedBox(height: 4.adaptiveSpacing),
                AdaptiveText(
                  'اختر القسم لمشاهدة الفيديوهات التعليمية',
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(VideoSection section) {
    return AdaptiveSectionCard(
      title: section.name,
      subtitle: section.description,
      backgroundColor: _getSectionColor(section.color),
      onTap: () => _navigateToSubjects(section),
      leading: AdaptiveIcon(
        Icons.video_collection,
        size: 32,
        color: Colors.white,
      ),
    );
  }

  Color _getSectionColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return AppTheme.primaryColor; // لون افتراضي في حالة الخطأ
    }
  }

  void _navigateToSubjects(VideoSection section) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoSubjectsPage(
          sectionId: section.id,
          hasSubscription: _hasSubscription,
          isFreeSection: section.isFree,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AdaptiveIcon(
            Icons.video_library_outlined,
            size: 80,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.adaptiveSpacing),
          AdaptiveText(
            'لا توجد أقسام فيديوهات متاحة',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
          SizedBox(height: 8.adaptiveSpacing),
          AdaptiveText(
            'تأكد من الاتصال بالإنترنت وحاول مرة أخرى',
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.adaptiveSpacing),
          AdaptiveElevatedButton(
            onPressed: _isRefreshing ? null : _refreshVideoSections,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AdaptiveIcon(Icons.refresh, size: 20, color: Colors.white),
                SizedBox(width: 8.adaptiveSpacing),
                AdaptiveText(
                  'إعادة المحاولة',
                  fontSize: 14,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
