import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/video_subject_model.dart';
import '../services/simple_data_service.dart';
import 'base_repository.dart';

/// Repository لمواد الفيديوهات مع دعم Offline-First
class VideoSubjectsRepository extends BaseRepository<VideoSubject> {
  static final VideoSubjectsRepository _instance = VideoSubjectsRepository._internal();
  static VideoSubjectsRepository get instance => _instance;
  VideoSubjectsRepository._internal();

  @override
  String get collectionName => 'video_subjects';

  @override
  String get storageKey => 'video_subjects';

  @override
  VideoSubject fromFirestore(Map<String, dynamic> data, String documentId) {
    return VideoSubject.fromFirestore(data, documentId);
  }

  @override
  Map<String, dynamic> toLocalMap(VideoSubject item) {
    return item.toLocalMap();
  }

  @override
  VideoSubject fromLocalMap(Map<String, dynamic> map) {
    return VideoSubject.fromLocalMap(map);
  }

  @override
  Map<String, dynamic> toFirestore(VideoSubject item) {
    return item.toFirestore();
  }

  @override
  Future<List<VideoSubject>> _loadFromLocalStorage() async {
    try {
      final file = File('${(await _persistentStorage.storageDirectory).path}/video_subjects.json');
      if (!await file.exists()) {
        // إذا لم توجد بيانات محلية، استخدم البيانات الافتراضية
        final defaultSubjects = SimpleDataService.instance.getVideoSubjects();
        debugPrint('📱 تم تحميل ${defaultSubjects.length} مادة فيديو من البيانات الافتراضية');
        return defaultSubjects;
      }

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final subjects = data.map((item) => fromLocalMap(item)).toList();

      debugPrint('📱 تم تحميل ${subjects.length} مادة فيديو من التخزين المحلي');
      return subjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد الفيديوهات من التخزين المحلي: $e');
      // في حالة الخطأ، إرجاع البيانات الافتراضية
      return SimpleDataService.instance.getVideoSubjects();
    }
  }

  @override
  Future<void> _saveToLocalStorage(List<VideoSubject> subjects) async {
    try {
      final file = File('${(await _persistentStorage.storageDirectory).path}/video_subjects.json');
      final data = subjects.map((subject) => toLocalMap(subject)).toList();
      await file.writeAsString(jsonEncode(data));
      
      debugPrint('✅ تم حفظ ${subjects.length} مادة فيديو في التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ مواد الفيديوهات في التخزين المحلي: $e');
    }
  }

  /// الحصول على مواد الفيديوهات النشطة فقط
  Future<List<VideoSubject>> getActiveSubjects() async {
    final allSubjects = await getLocalData();
    return allSubjects.where((subject) => subject.isActive).toList();
  }

  /// الحصول على مواد الفيديوهات لقسم معين
  Future<List<VideoSubject>> getSubjectsBySection(String sectionId) async {
    final allSubjects = await getLocalData();
    return allSubjects
        .where((subject) => subject.sectionId == sectionId && subject.isActive)
        .toList();
  }

  /// البحث في مواد الفيديوهات
  Future<List<VideoSubject>> searchSubjects(String query) async {
    final allSubjects = await getLocalData();
    final lowerQuery = query.toLowerCase();
    
    return allSubjects
        .where((subject) =>
            subject.name.toLowerCase().contains(lowerQuery) ||
            subject.description.toLowerCase().contains(lowerQuery))
        .toList();
  }

  /// الحصول على مادة فيديو بواسطة المعرف
  Future<VideoSubject?> getSubjectById(String id) async {
    final allSubjects = await getLocalData();
    try {
      return allSubjects.firstWhere((subject) => subject.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تهيئة البيانات الأولية
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة repository مواد الفيديوهات...');
      
      // تحميل البيانات المحلية أولاً
      final localSubjects = await getLocalData();
      debugPrint('📱 تم تحميل ${localSubjects.length} مادة فيديو محلياً');
      
      // التحقق من التحديثات في الخلفية
      _checkForUpdatesInBackground();
      
      debugPrint('✅ تم تهيئة repository مواد الفيديوهات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة repository مواد الفيديوهات: $e');
    }
  }

  /// التحقق من التحديثات في الخلفية
  void _checkForUpdatesInBackground() {
    Future.delayed(Duration.zero, () async {
      try {
        if (await hasRemoteUpdates()) {
          debugPrint('🔄 يوجد تحديثات جديدة لمواد الفيديوهات، بدء المزامنة...');
          await fetchAndCacheRemoteData();
        }
      } catch (e) {
        debugPrint('❌ خطأ في التحقق من تحديثات مواد الفيديوهات: $e');
      }
    });
  }

  /// مزامنة دورية (يمكن استدعاؤها من خدمة المزامنة في الخلفية)
  Future<void> periodicSync() async {
    try {
      debugPrint('🔄 مزامنة دورية لمواد الفيديوهات...');
      await syncIfNeeded();
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الدورية لمواد الفيديوهات: $e');
    }
  }

  /// الحصول على إحصائيات مواد الفيديوهات
  Future<Map<String, int>> getSubjectsStats() async {
    final allSubjects = await getLocalData();
    
    return {
      'total': allSubjects.length,
      'active': allSubjects.where((s) => s.isActive).length,
      'inactive': allSubjects.where((s) => !s.isActive).length,
    };
  }

  /// الحصول على مواد الفيديوهات مجمعة حسب القسم
  Future<Map<String, List<VideoSubject>>> getSubjectsGroupedBySection() async {
    final allSubjects = await getActiveSubjects();
    final Map<String, List<VideoSubject>> grouped = {};
    
    for (final subject in allSubjects) {
      if (!grouped.containsKey(subject.sectionId)) {
        grouped[subject.sectionId] = [];
      }
      grouped[subject.sectionId]!.add(subject);
    }
    
    return grouped;
  }
}
