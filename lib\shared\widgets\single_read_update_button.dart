import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/single_read_data_service.dart';

/// زر التحديث اليدوي الوحيد - يظهر في جميع الصفحات
/// هذا الزر هو الطريقة الوحيدة لتحديث البيانات (قراءة واحدة فقط)
class SingleReadUpdateButton extends StatelessWidget {
  const SingleReadUpdateButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SingleReadDataService>(
      builder: (context, dataService, child) {
        return Positioned(
          top: MediaQuery.of(context).padding.top + 60,
          right: 20,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(25),
                onTap: dataService.isLoading
                    ? null
                    : () => _showUpdateDialog(context),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (dataService.isLoading)
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor,
                            ),
                          ),
                        )
                      else
                        Icon(
                          Icons.refresh,
                          color: Theme.of(context).primaryColor,
                          size: 20,
                        ),
                      const SizedBox(width: 8),
                      Text(
                        dataService.isLoading ? 'جاري التحديث...' : 'تحديث',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// عرض حوار التحديث مع مؤشر التقدم
  void _showUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const UpdateProgressDialog(),
    );
  }
}

/// حوار مؤشر التقدم للتحديث
class UpdateProgressDialog extends StatefulWidget {
  const UpdateProgressDialog({super.key});

  @override
  State<UpdateProgressDialog> createState() => _UpdateProgressDialogState();
}

class _UpdateProgressDialogState extends State<UpdateProgressDialog> {
  @override
  void initState() {
    super.initState();
    // تأخير بدء التحديث حتى انتهاء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startUpdate();
    });
  }

  Future<void> _startUpdate() async {
    final dataService = context.read<SingleReadDataService>();
    final success = await dataService.performSingleRead();

    if (mounted) {
      Navigator.of(context).pop();

      if (success) {
        _showSuccessMessage();
      } else {
        _showErrorMessage(dataService.error ?? 'حدث خطأ غير معروف');
      }
    }
  }

  void _showSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text('تم تحديث البيانات بنجاح'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorMessage(String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text('فشل التحديث: $error')),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          textColor: Colors.white,
          onPressed: () => _showUpdateDialog(context),
        ),
      ),
    );
  }

  void _showUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const UpdateProgressDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SingleReadDataService>(
      builder: (context, dataService, child) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحديث
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.cloud_download,
                  size: 40,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 24),

              // عنوان
              Text(
                'تحديث البيانات',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // مؤشر التقدم
              LinearProgressIndicator(
                value: dataService.progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 12),

              // نسبة التقدم
              Text(
                '${(dataService.progress * 100).toInt()}%',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 8),

              // الخطوة الحالية
              Text(
                dataService.currentStep,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // معلومات إضافية
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: Colors.blue[700],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'قراءة واحدة فقط',
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'يتم تحميل جميع البيانات بقراءة واحدة لتوفير استهلاك البيانات',
                      style: TextStyle(color: Colors.blue[600], fontSize: 11),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
