import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../core/models/section.dart';
import '../models/subject_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../models/question_model.dart';

/// خدمة فرز وتوزيع البيانات من التخزين المحلي
/// تأخذ البيانات من الوثائق المحفوظة محلياً وتوزعها على الصفحات المناسبة
class DataDistributionService {
  static final DataDistributionService _instance =
      DataDistributionService._internal();
  static DataDistributionService get instance => _instance;
  DataDistributionService._internal();

  // مفاتيح التخزين المحلي
  static const String _generalDataKey = 'general_data';
  static const String _subjectPrefix = 'subject_';
  static const String _freeSubjectPrefix = 'free_subject_';

  // كاش البيانات المفروزة
  List<Section>? _cachedSections;
  List<Subject>? _cachedSubjects;
  List<VideoSection>? _cachedVideoSections;
  List<VideoSubject>? _cachedVideoSubjects;

  Map<String, List<Unit>> _cachedUnits = {};
  Map<String, List<Lesson>> _cachedLessons = {};
  Map<String, List<Question>> _cachedQuestions = {};
  Map<String, List<VideoUnit>> _cachedVideoUnits = {};
  Map<String, List<VideoLesson>> _cachedVideoLessons = {};
  Map<String, List<Video>> _cachedVideos = {};

  // ═══════════════════════════════════════════════════════════════
  // 🔄 تحديث الكاش من التخزين المحلي
  // ═══════════════════════════════════════════════════════════════

  /// تحديث جميع البيانات من التخزين المحلي
  Future<void> refreshAllData() async {
    try {
      debugPrint(
        '🔄 [STUDENT MONITOR] تحديث جميع البيانات من التخزين المحلي...',
      );
      debugPrint('🕐 [STUDENT MONITOR] الوقت: ${DateTime.now()}');

      await _loadGeneralData();
      await _loadSubjectData();

      // 🔍 طباعة إحصائيات مفصلة
      printDataStatistics();

      debugPrint('✅ [STUDENT MONITOR] تم تحديث جميع البيانات بنجاح');
      debugPrint('🕐 [STUDENT MONITOR] انتهى في: ${DateTime.now()}');
    } catch (e) {
      debugPrint('❌ [STUDENT MONITOR] خطأ في تحديث البيانات: $e');
    }
  }

  /// تحميل البيانات العامة
  Future<void> _loadGeneralData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(_generalDataKey);

      if (dataString != null) {
        final data = jsonDecode(dataString);
        final generalData = data['data'];

        // فرز الأقسام والمواد
        _cachedSections =
            (generalData['test_sections'] as List?)
                ?.map(
                  (item) =>
                      Section.fromLocalMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        _cachedSubjects =
            (generalData['test_subjects'] as List?)
                ?.map(
                  (item) => Subject.fromMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        _cachedVideoSections =
            (generalData['video_sections'] as List?)
                ?.map(
                  (item) =>
                      VideoSection.fromMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        _cachedVideoSubjects =
            (generalData['video_subjects'] as List?)
                ?.map(
                  (item) =>
                      VideoSubject.fromMap(Map<String, dynamic>.from(item)),
                )
                .toList() ??
            [];

        debugPrint('✅ تم تحميل البيانات العامة:');
        debugPrint('   - أقسام الاختبارات: ${_cachedSections?.length ?? 0}');
        debugPrint('   - مواد الاختبارات: ${_cachedSubjects?.length ?? 0}');
        debugPrint(
          '   - أقسام الفيديوهات: ${_cachedVideoSections?.length ?? 0}',
        );
        debugPrint(
          '   - مواد الفيديوهات: ${_cachedVideoSubjects?.length ?? 0}',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات العامة: $e');
    }
  }

  /// تحميل بيانات المواد
  Future<void> _loadSubjectData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      // تحميل بيانات المواد المدفوعة والمجانية
      for (final key in keys) {
        if (key.startsWith(_subjectPrefix) ||
            key.startsWith(_freeSubjectPrefix)) {
          await _loadSingleSubjectData(key);
        }
      }

      debugPrint('✅ تم تحميل بيانات ${_cachedUnits.length} مادة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات المواد: $e');
    }
  }

  /// تحميل بيانات مادة واحدة
  Future<void> _loadSingleSubjectData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataString = prefs.getString(key);

      if (dataString != null) {
        final data = jsonDecode(dataString);
        final subjectData = data['data'];
        final subjectInfo = data['subject_info'];
        final subjectId = subjectInfo['id'];

        // فرز محتوى المادة
        if (subjectData['units'] != null) {
          _cachedUnits[subjectId] = (subjectData['units'] as List)
              .map((item) => Unit.fromMap(Map<String, dynamic>.from(item)))
              .toList();
        }

        if (subjectData['lessons'] != null) {
          _cachedLessons[subjectId] = (subjectData['lessons'] as List)
              .map((item) => Lesson.fromMap(Map<String, dynamic>.from(item)))
              .toList();
        }

        if (subjectData['questions'] != null) {
          _cachedQuestions[subjectId] = (subjectData['questions'] as List)
              .map((item) => Question.fromMap(Map<String, dynamic>.from(item)))
              .toList();
        }

        if (subjectData['video_units'] != null) {
          _cachedVideoUnits[subjectId] = (subjectData['video_units'] as List)
              .map((item) => VideoUnit.fromMap(Map<String, dynamic>.from(item)))
              .toList();
        }

        if (subjectData['video_lessons'] != null) {
          _cachedVideoLessons[subjectId] =
              (subjectData['video_lessons'] as List)
                  .map(
                    (item) =>
                        VideoLesson.fromMap(Map<String, dynamic>.from(item)),
                  )
                  .toList();
        }

        if (subjectData['videos'] != null) {
          _cachedVideos[subjectId] = (subjectData['videos'] as List)
              .map((item) => Video.fromMap(Map<String, dynamic>.from(item)))
              .toList();
        }

        debugPrint('✅ تم تحميل بيانات المادة: $subjectId');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات المادة $key: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // 📊 واجهات الحصول على البيانات
  // ═══════════════════════════════════════════════════════════════

  /// الحصول على جميع الأقسام
  List<Section> getAllSections() {
    return _cachedSections ?? [];
  }

  /// الحصول على الأقسام المجانية
  List<Section> getFreeSections() {
    return getAllSections().where((section) => section.isFree).toList();
  }

  /// الحصول على الأقسام المدفوعة
  List<Section> getPaidSections() {
    return getAllSections().where((section) => !section.isFree).toList();
  }

  /// الحصول على جميع المواد
  List<Subject> getAllSubjects() {
    return _cachedSubjects ?? [];
  }

  /// الحصول على مواد قسم معين
  List<Subject> getSubjectsBySection(String sectionId) {
    return getAllSubjects()
        .where((subject) => subject.sectionId == sectionId)
        .toList();
  }

  /// الحصول على المواد المجانية لقسم معين
  List<Subject> getFreeSubjectsBySection(String sectionId) {
    return getSubjectsBySection(
      sectionId,
    ).where((subject) => subject.isFree).toList();
  }

  /// الحصول على المواد المدفوعة لقسم معين
  List<Subject> getPaidSubjectsBySection(String sectionId) {
    return getSubjectsBySection(
      sectionId,
    ).where((subject) => !subject.isFree).toList();
  }

  /// الحصول على جميع أقسام الفيديو
  List<VideoSection> getAllVideoSections() {
    return _cachedVideoSections ?? [];
  }

  /// الحصول على أقسام الفيديو المجانية
  List<VideoSection> getFreeVideoSections() {
    return getAllVideoSections().where((section) => section.isFree).toList();
  }

  /// الحصول على أقسام الفيديو المدفوعة
  List<VideoSection> getPaidVideoSections() {
    return getAllVideoSections().where((section) => !section.isFree).toList();
  }

  /// الحصول على جميع مواد الفيديو
  List<VideoSubject> getAllVideoSubjects() {
    return _cachedVideoSubjects ?? [];
  }

  /// الحصول على مواد الفيديو لقسم معين
  List<VideoSubject> getVideoSubjectsBySection(String sectionId) {
    return getAllVideoSubjects()
        .where((subject) => subject.sectionId == sectionId)
        .toList();
  }

  /// الحصول على مواد الفيديو المجانية لقسم معين
  List<VideoSubject> getFreeVideoSubjectsBySection(String sectionId) {
    return getVideoSubjectsBySection(
      sectionId,
    ).where((subject) => subject.isFree).toList();
  }

  /// الحصول على مواد الفيديو المدفوعة لقسم معين
  List<VideoSubject> getPaidVideoSubjectsBySection(String sectionId) {
    return getVideoSubjectsBySection(
      sectionId,
    ).where((subject) => !subject.isFree).toList();
  }

  /// الحصول على وحدات مادة معينة
  List<Unit> getUnitsBySubject(String subjectId) {
    return _cachedUnits[subjectId] ?? [];
  }

  /// الحصول على دروس مادة معينة
  List<Lesson> getLessonsBySubject(String subjectId) {
    return _cachedLessons[subjectId] ?? [];
  }

  /// الحصول على أسئلة مادة معينة
  List<Question> getQuestionsBySubject(String subjectId) {
    return _cachedQuestions[subjectId] ?? [];
  }

  /// الحصول على وحدات فيديو لمادة معينة
  List<VideoUnit> getVideoUnitsBySubject(String subjectId) {
    return _cachedVideoUnits[subjectId] ?? [];
  }

  /// الحصول على دروس فيديو لمادة معينة
  List<VideoLesson> getVideoLessonsBySubject(String subjectId) {
    return _cachedVideoLessons[subjectId] ?? [];
  }

  /// الحصول على فيديوهات مادة معينة
  List<Video> getVideosBySubject(String subjectId) {
    return _cachedVideos[subjectId] ?? [];
  }

  // ═══════════════════════════════════════════════════════════════
  // 🧹 تنظيف الكاش
  // ═══════════════════════════════════════════════════════════════

  /// مسح جميع البيانات المخزنة في الكاش
  void clearCache() {
    _cachedSections = null;
    _cachedSubjects = null;
    _cachedVideoSections = null;
    _cachedVideoSubjects = null;
    _cachedUnits.clear();
    _cachedLessons.clear();
    _cachedQuestions.clear();
    _cachedVideoUnits.clear();
    _cachedVideoLessons.clear();
    _cachedVideos.clear();

    debugPrint('🧹 تم مسح جميع البيانات من الكاش');
  }

  /// التحقق من وجود بيانات في الكاش
  bool hasData() {
    return (_cachedSections?.isNotEmpty ?? false) ||
        (_cachedVideoSections?.isNotEmpty ?? false) ||
        _cachedUnits.isNotEmpty ||
        _cachedVideoUnits.isNotEmpty;
  }

  /// طباعة إحصائيات البيانات المحملة
  void printDataStatistics() {
    debugPrint('📊 [STUDENT MONITOR] ═══ إحصائيات البيانات المحملة ═══');
    debugPrint('🕐 [STUDENT MONITOR] الوقت: ${DateTime.now()}');

    // إحصائيات الاختبارات
    debugPrint('📝 [STUDENT MONITOR] بيانات الاختبارات:');
    debugPrint('   - الأقسام: ${_cachedSections?.length ?? 0}');
    debugPrint('   - المواد: ${_cachedSubjects?.length ?? 0}');
    debugPrint(
      '   - الوحدات: ${_cachedUnits.values.fold(0, (sum, units) => sum + units.length)}',
    );
    debugPrint(
      '   - الدروس: ${_cachedLessons.values.fold(0, (sum, lessons) => sum + lessons.length)}',
    );
    debugPrint(
      '   - الأسئلة: ${_cachedQuestions.values.fold(0, (sum, questions) => sum + questions.length)}',
    );

    // إحصائيات الفيديوهات
    debugPrint('🎥 [STUDENT MONITOR] بيانات الفيديوهات:');
    debugPrint('   - الأقسام: ${_cachedVideoSections?.length ?? 0}');
    debugPrint('   - المواد: ${_cachedVideoSubjects?.length ?? 0}');
    debugPrint(
      '   - الوحدات: ${_cachedVideoUnits.values.fold(0, (sum, units) => sum + units.length)}',
    );
    debugPrint(
      '   - الدروس: ${_cachedVideoLessons.values.fold(0, (sum, lessons) => sum + lessons.length)}',
    );
    debugPrint(
      '   - الفيديوهات: ${_cachedVideos.values.fold(0, (sum, videos) => sum + videos.length)}',
    );

    // تفاصيل الأقسام
    if (_cachedSections?.isNotEmpty ?? false) {
      debugPrint('📋 [STUDENT MONITOR] تفاصيل الأقسام:');
      for (var section in _cachedSections!) {
        final subjectCount =
            _cachedSubjects?.where((s) => s.sectionId == section.id).length ??
            0;
        debugPrint(
          '   - ${section.name}: $subjectCount مادة (مجاني: ${section.isFree})',
        );
      }
    }

    // تفاصيل أقسام الفيديو
    if (_cachedVideoSections?.isNotEmpty ?? false) {
      debugPrint('🎬 [STUDENT MONITOR] تفاصيل أقسام الفيديو:');
      for (var section in _cachedVideoSections!) {
        final subjectCount =
            _cachedVideoSubjects
                ?.where((s) => s.sectionId == section.id)
                .length ??
            0;
        debugPrint(
          '   - ${section.name}: $subjectCount مادة (مجاني: ${section.isFree})',
        );
      }
    }

    debugPrint('📊 [STUDENT MONITOR] ═══════════════════════════════════════');
  }
}
