import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/unified_data_generator.dart';
import '../../../../shared/services/auto_unified_data_service.dart';

class UnifiedDataManagementPage extends StatefulWidget {
  const UnifiedDataManagementPage({super.key});

  @override
  State<UnifiedDataManagementPage> createState() =>
      _UnifiedDataManagementPageState();
}

class _UnifiedDataManagementPageState extends State<UnifiedDataManagementPage> {
  final UnifiedDataGenerator _generator = UnifiedDataGenerator.instance;
  final AutoUnifiedDataService _autoService = AutoUnifiedDataService.instance;
  bool _isLoading = false;
  bool _unifiedDataExists = false;

  @override
  void initState() {
    super.initState();
    _checkUnifiedDataStatus();
  }

  Future<void> _checkUnifiedDataStatus() async {
    setState(() => _isLoading = true);
    try {
      final exists = await _generator.checkUnifiedDataExists();
      setState(() => _unifiedDataExists = exists);
    } catch (e) {
      _showErrorSnackBar('خطأ في التحقق من حالة البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateUnifiedData() async {
    setState(() => _isLoading = true);
    try {
      final success = await _generator.generateUnifiedData();
      if (success) {
        _showSuccessSnackBar('تم إنشاء البيانات الموحدة بنجاح');
        await _checkUnifiedDataStatus();
      } else {
        _showErrorSnackBar('فشل في إنشاء البيانات الموحدة');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في إنشاء البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _updateAllUsersData() async {
    setState(() => _isLoading = true);
    try {
      final success = await _generator.updateAllUsersUnifiedData();
      if (success) {
        _showSuccessSnackBar('تم تحديث بيانات جميع المستخدمين بنجاح');
      } else {
        _showErrorSnackBar('فشل في تحديث بيانات بعض المستخدمين');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحديث بيانات المستخدمين: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fullUpdate() async {
    setState(() => _isLoading = true);
    try {
      final success = await _autoService.manualFullUpdate();
      if (success) {
        _showSuccessSnackBar('تم التحديث الشامل بنجاح');
        await _checkUnifiedDataStatus();
      } else {
        _showErrorSnackBar('فشل في التحديث الشامل');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التحديث الشامل: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteUnifiedData() async {
    final confirmed = await _showConfirmationDialog(
      'حذف البيانات الموحدة',
      'هل أنت متأكد من حذف البيانات الموحدة؟ سيؤدي هذا إلى استخدام النظام القديم (قراءات متعددة).',
    );

    if (!confirmed) return;

    setState(() => _isLoading = true);
    try {
      final success = await _generator.deleteUnifiedData();
      if (success) {
        _showSuccessSnackBar('تم حذف البيانات الموحدة');
        await _checkUnifiedDataStatus();
      } else {
        _showErrorSnackBar('فشل في حذف البيانات الموحدة');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف البيانات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text(
                  'تأكيد',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة البيانات الموحدة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildStatusCard(),
                  SizedBox(height: 20.h),
                  _buildInfoCard(),
                  SizedBox(height: 20.h),
                  _buildActionsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة البيانات الموحدة',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Icon(
                  _unifiedDataExists ? Icons.check_circle : Icons.error,
                  color: _unifiedDataExists ? Colors.green : Colors.red,
                  size: 24.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  _unifiedDataExists
                      ? 'البيانات الموحدة موجودة'
                      : 'البيانات الموحدة غير موجودة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: _unifiedDataExists ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              _unifiedDataExists
                  ? 'التطبيق يستخدم نظام القراءة الواحدة (موصى به)'
                  : 'التطبيق يستخدم النظام القديم (قراءات متعددة)',
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات مهمة',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(height: 12.h),
            _buildInfoItem(
              '🎯',
              'نظام القراءة الواحدة',
              'يقلل استهلاك Firebase بنسبة 99% ويحسن الأداء',
            ),
            _buildInfoItem(
              '🔗',
              'نظام المراجع الذكي',
              'يوفر 95%+ من مساحة التخزين باستخدام المراجع بدلاً من نسخ البيانات',
            ),
            _buildInfoItem(
              '🤖',
              'التحديث التلقائي',
              'البيانات تتحدث تلقائياً عند إضافة/تعديل/حذف المحتوى',
            ),
            _buildInfoItem(
              '🔧',
              'التحديث اليدوي',
              'متوفر كخيار احتياطي في حال فشل التحديث التلقائي',
            ),
            _buildInfoItem(
              '📊',
              'البيانات العامة',
              'تشمل: الأقسام + المواد + المحتوى المجاني',
            ),
            _buildInfoItem(
              '👥',
              'البيانات الخاصة (مراجع)',
              'تشمل: بيانات الاشتراك + مراجع المحتوى المدفوع فقط',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String emoji, String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: TextStyle(fontSize: 20.sp)),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'الإجراءات',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        SizedBox(height: 16.h),
        _buildActionButton(
          'إنشاء البيانات الموحدة',
          'تجميع جميع البيانات في وثيقة واحدة لتحقيق القراءة الواحدة',
          Icons.create,
          Colors.green,
          _generateUnifiedData,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          'تحديث بيانات جميع المستخدمين',
          'تحديث البيانات الموحدة لجميع المستخدمين المسجلين',
          Icons.people,
          Colors.blue,
          _updateAllUsersData,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          'تحديث شامل (تلقائي + يدوي)',
          'تحديث البيانات العامة وبيانات جميع المستخدمين',
          Icons.refresh,
          Colors.purple,
          _fullUpdate,
        ),
        SizedBox(height: 12.h),
        _buildActionButton(
          'حذف البيانات الموحدة',
          'العودة للنظام القديم (قراءات متعددة)',
          Icons.delete,
          Colors.red,
          _deleteUnifiedData,
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24.sp),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16.sp, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}
