org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# تحسين الشبكة والأداء
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# إعدادات الشبكة
systemProp.http.socketTimeout=60000
systemProp.http.connectionTimeout=60000
systemProp.https.socketTimeout=60000
systemProp.https.connectionTimeout=60000

# تحسين Android Build
android.enableR8.fullMode=true
