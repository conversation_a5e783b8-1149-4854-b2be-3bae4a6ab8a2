import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';

import 'flavors.dart';
import 'core/theme/app_theme.dart';
import 'features/student/presentation/pages/simple_student_home_page.dart';
import 'features/admin/presentation/pages/admin_home_page.dart';
import 'shared/services/new_subscription_service.dart';
import 'shared/services/exam_service.dart';
import 'shared/services/content_service.dart';
import 'shared/services/pricing_service.dart';
import 'shared/services/firebase_service.dart';
// النظام الجديد للقراءة الواحدة
import 'shared/services/single_read_data_service.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // تهيئة الخدمات عند بدء التطبيق
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeServices();
    });
  }

  /// تهيئة الخدمات حسب نوع التطبيق
  Future<void> _initializeServices() async {
    if (F.isStudent) {
      // تهيئة النظام الجديد للقراءة الواحدة
      await SingleReadDataService.instance.initialize();
    } else {
      // تهيئة Firebase Authentication لتطبيق الإدارة
      await _initializeAdminServices();
    }
  }

  /// تهيئة خدمات تطبيق الإدارة
  Future<void> _initializeAdminServices() async {
    try {
      // تسجيل دخول مجهول للإدارة للوصول إلى Firebase
      await FirebaseService.instance.signInAnonymously();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمات الإدارة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد حجم التصميم بناءً على نوع الجهاز
    Size designSize = const Size(375, 812); // افتراضي للهاتف

    // للكمبيوتر، نستخدم حجم أكبر
    if (kIsWeb ||
        (!kIsWeb &&
            (Platform.isWindows || Platform.isMacOS || Platform.isLinux))) {
      designSize = const Size(1200, 800); // للكمبيوتر
    }

    return ScreenUtilInit(
      designSize: designSize,
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiProvider(
          providers: [
            // النظام الجديد للقراءة الواحدة
            ChangeNotifierProvider.value(value: SingleReadDataService.instance),
            ChangeNotifierProvider(create: (_) => SubscriptionService.instance),
            // الخدمات الحالية
            ChangeNotifierProvider(create: (_) => ExamService.instance),
            ChangeNotifierProvider(create: (_) => ContentService.instance),
            ChangeNotifierProvider(create: (_) => PricingService.instance),
          ],
          child: MaterialApp(
            title: F.title,
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            home: F.isStudent
                ? const SimpleStudentHomePage()
                : const AdminHomePage(),
          ),
        );
      },
    );
  }
}
