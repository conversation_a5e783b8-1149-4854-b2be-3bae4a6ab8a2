// ملف وهمي مؤقت - سيتم استبداله بالنظام الجديد
export 'new_subscription_service.dart';

// إعادة تصدير الكلاس بالاسم القديم للتوافق المؤقت
class SubscriptionService {
  static SubscriptionService get instance => SubscriptionService();
  
  bool get isLoading => false;
  List<dynamic> get allSubjects => [];
  List<dynamic> get paidSubjects => [];
  List<dynamic> get subscribedSubjects => [];
  
  Future<void> initialize() async {}
  void addListener(Function listener) {}
  void removeListener(Function listener) {}
  bool isSubscribedToSubject(String subjectId) => false;
  Future<bool> useSubscriptionCode(String code) async => false;
}
