// ملف وهمي مؤقت - يحتوي على جميع الدوال المطلوبة للتوافق
import 'package:flutter/foundation.dart';
import '../models/subject_model.dart';
import '../models/user_subscription_model.dart';

export 'new_subscription_service.dart';

// كلاس وهمي للتوافق مع الكود القديم
class SubscriptionService extends ChangeNotifier {
  static SubscriptionService get instance => SubscriptionService();

  // خصائص وهمية
  bool get isLoading => false;
  List<Subject> get allSubjects => [];
  List<Subject> get paidSubjects => [];
  List<Subject> get subscribedSubjects => [];
  UserSubscription? get currentSubscription => null;

  // دوال وهمية للتوافق
  Future<void> initialize() async {}

  @override
  void addListener(VoidCallback listener) {}

  @override
  void removeListener(VoidCallback listener) {}

  bool isSubscribedToSubject(String subjectId) => false;
  bool isSubscribedToVideoSubject(String videoSubjectId) => false;
  bool hasActiveSubscription() => false;
  Future<bool> useSubscriptionCode(String code) async => false;
  Future<void> loadUserSubscription() async {}
  Future<List<dynamic>> getSubscribedVideoSubjects() async => [];
  DateTime? getSubjectActivationDate(String subjectId) => null;
  Future<Map<String, String>> getDeviceInfo() async => {};
}
