import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart';
import 'offline_first_service.dart';
import 'local_notifications_service.dart';

/// خدمة المزامنة في الخلفية
/// تستخدم Workmanager لتنفيذ مزامنة دورية حتى عندما يكون التطبيق مغلق
class BackgroundSyncService {
  static const String _syncTaskName = 'smart_edu_background_sync';
  static const String _uniqueTaskName = 'smart_edu_sync_unique';
  
  static final BackgroundSyncService _instance = BackgroundSyncService._internal();
  static BackgroundSyncService get instance => _instance;
  BackgroundSyncService._internal();

  bool _isInitialized = false;
  Timer? _foregroundSyncTimer;

  /// تهيئة خدمة المزامنة في الخلفية
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      debugPrint('🔄 تهيئة خدمة المزامنة في الخلفية...');

      // تهيئة Workmanager
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: kDebugMode,
      );

      // جدولة المهمة الدورية
      await _schedulePeriodicSync();

      // بدء المزامنة في المقدمة
      _startForegroundSync();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة المزامنة في الخلفية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة المزامنة في الخلفية: $e');
    }
  }

  /// جدولة المهمة الدورية
  Future<void> _schedulePeriodicSync() async {
    try {
      // إلغاء المهام السابقة
      await Workmanager().cancelByUniqueName(_uniqueTaskName);

      // جدولة مهمة دورية كل 30 دقيقة
      await Workmanager().registerPeriodicTask(
        _uniqueTaskName,
        _syncTaskName,
        frequency: const Duration(minutes: 30),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
        backoffPolicy: BackoffPolicy.exponential,
        backoffPolicyDelay: const Duration(minutes: 5),
      );

      debugPrint('⏰ تم جدولة المزامنة الدورية (كل 30 دقيقة)');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة المزامنة الدورية: $e');
    }
  }

  /// بدء المزامنة في المقدمة (عندما يكون التطبيق مفتوح)
  void _startForegroundSync() {
    // إيقاف المؤقت السابق إن وجد
    _foregroundSyncTimer?.cancel();

    // بدء مؤقت جديد للمزامنة كل 15 دقيقة في المقدمة
    _foregroundSyncTimer = Timer.periodic(
      const Duration(minutes: 15),
      (timer) => _performForegroundSync(),
    );

    debugPrint('⏰ تم بدء المزامنة في المقدمة (كل 15 دقيقة)');
  }

  /// تنفيذ المزامنة في المقدمة
  Future<void> _performForegroundSync() async {
    try {
      debugPrint('🔄 بدء المزامنة في المقدمة...');
      
      final offlineService = OfflineFirstService.instance;
      await offlineService.forceRefreshAll();
      
      debugPrint('✅ تم إنجاز المزامنة في المقدمة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة في المقدمة: $e');
    }
  }

  /// مزامنة فورية
  Future<void> syncNow() async {
    try {
      debugPrint('🔄 بدء المزامنة الفورية...');
      
      final offlineService = OfflineFirstService.instance;
      await offlineService.forceRefreshAll();
      
      debugPrint('✅ تم إنجاز المزامنة الفورية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الفورية: $e');
      rethrow;
    }
  }

  /// إيقاف المزامنة في الخلفية
  Future<void> stopBackgroundSync() async {
    try {
      await Workmanager().cancelByUniqueName(_uniqueTaskName);
      _foregroundSyncTimer?.cancel();
      debugPrint('⏹️ تم إيقاف المزامنة في الخلفية');
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف المزامنة في الخلفية: $e');
    }
  }

  /// إعادة تشغيل المزامنة في الخلفية
  Future<void> restartBackgroundSync() async {
    try {
      await stopBackgroundSync();
      await _schedulePeriodicSync();
      _startForegroundSync();
      debugPrint('🔄 تم إعادة تشغيل المزامنة في الخلفية');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تشغيل المزامنة في الخلفية: $e');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _foregroundSyncTimer?.cancel();
  }

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}

/// Callback dispatcher للمهام في الخلفية
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      debugPrint('🔄 تنفيذ مهمة المزامنة في الخلفية: $task');
      
      switch (task) {
        case BackgroundSyncService._syncTaskName:
          await _performBackgroundSync();
          break;
        default:
          debugPrint('❓ مهمة غير معروفة: $task');
          return false;
      }
      
      debugPrint('✅ تم إنجاز مهمة المزامنة في الخلفية بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تنفيذ مهمة المزامنة في الخلفية: $e');
      return false;
    }
  });
}

/// تنفيذ المزامنة في الخلفية
Future<void> _performBackgroundSync() async {
  try {
    // ملاحظة: في الخلفية، نحتاج لإعادة تهيئة الخدمات
    // هذا مثال مبسط - في التطبيق الحقيقي نحتاج لتهيئة كاملة
    
    debugPrint('🔄 بدء المزامنة في الخلفية...');
    
    // يمكن إضافة منطق المزامنة هنا
    // مثل التحقق من التحديثات وإرسال إشعارات محلية
    
    debugPrint('✅ تم إنجاز المزامنة في الخلفية');
  } catch (e) {
    debugPrint('❌ خطأ في المزامنة في الخلفية: $e');
    rethrow;
  }
}

/// خدمة إدارة الإشعارات التلقائية
class AutoNotificationService {
  static final AutoNotificationService _instance = AutoNotificationService._internal();
  static AutoNotificationService get instance => _instance;
  AutoNotificationService._internal();

  Timer? _notificationTimer;

  /// بدء خدمة الإشعارات التلقائية
  void startAutoNotifications() {
    // إيقاف المؤقت السابق إن وجد
    _notificationTimer?.cancel();

    // بدء مؤقت للتحقق من الإشعارات كل 5 دقائق
    _notificationTimer = Timer.periodic(
      const Duration(minutes: 5),
      (timer) => _checkForNewNotifications(),
    );

    debugPrint('🔔 تم بدء خدمة الإشعارات التلقائية');
  }

  /// التحقق من الإشعارات الجديدة
  Future<void> _checkForNewNotifications() async {
    try {
      final notificationsService = LocalNotificationsService.instance;
      final unreadCount = notificationsService.unreadCount;
      
      if (unreadCount > 0) {
        debugPrint('🔔 يوجد $unreadCount إشعار غير مقروء');
        // يمكن إضافة منطق إضافي هنا مثل إرسال إشعار نظام
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الإشعارات: $e');
    }
  }

  /// إيقاف خدمة الإشعارات التلقائية
  void stopAutoNotifications() {
    _notificationTimer?.cancel();
    debugPrint('⏹️ تم إيقاف خدمة الإشعارات التلقائية');
  }

  /// تنظيف الموارد
  void dispose() {
    _notificationTimer?.cancel();
  }
}

/// خدمة مراقبة الاتصال بالإنترنت
class NetworkMonitorService {
  static final NetworkMonitorService _instance = NetworkMonitorService._internal();
  static NetworkMonitorService get instance => _instance;
  NetworkMonitorService._internal();

  bool _isConnected = true;
  Timer? _connectionCheckTimer;

  /// بدء مراقبة الاتصال
  void startMonitoring() {
    // إيقاف المؤقت السابق إن وجد
    _connectionCheckTimer?.cancel();

    // بدء مؤقت للتحقق من الاتصال كل دقيقة
    _connectionCheckTimer = Timer.periodic(
      const Duration(minutes: 1),
      (timer) => _checkConnection(),
    );

    debugPrint('📡 تم بدء مراقبة الاتصال بالإنترنت');
  }

  /// التحقق من الاتصال
  Future<void> _checkConnection() async {
    try {
      // يمكن إضافة منطق التحقق من الاتصال هنا
      // مثل ping إلى خادم Firebase
      
      final wasConnected = _isConnected;
      // افتراض أن الاتصال متاح (يمكن تحسين هذا)
      _isConnected = true;
      
      if (!wasConnected && _isConnected) {
        debugPrint('📡 تم استعادة الاتصال بالإنترنت - بدء المزامنة');
        await BackgroundSyncService.instance.syncNow();
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الاتصال: $e');
      _isConnected = false;
    }
  }

  /// إيقاف مراقبة الاتصال
  void stopMonitoring() {
    _connectionCheckTimer?.cancel();
    debugPrint('⏹️ تم إيقاف مراقبة الاتصال');
  }

  /// تنظيف الموارد
  void dispose() {
    _connectionCheckTimer?.cancel();
  }

  /// الحصول على حالة الاتصال
  bool get isConnected => _isConnected;
}
