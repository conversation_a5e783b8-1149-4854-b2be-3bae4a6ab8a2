import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/models/subject_model.dart';
import '../../../../shared/services/unified_offline_service.dart';

/// مثال لصفحة الأقسام الجديدة باستخدام النظام الجديد Offline-First
/// هذا مثال توضيحي لكيفية استخدام النظام الجديد
class NewSectionsPageExample extends StatefulWidget {
  const NewSectionsPageExample({super.key});

  @override
  State<NewSectionsPageExample> createState() => _NewSectionsPageExampleState();
}

class _NewSectionsPageExampleState extends State<NewSectionsPageExample> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  List<Section> _sections = [];
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadSectionsImmediately();
  }

  /// تحميل فوري للأقسام من الذاكرة المحلية
  Future<void> _loadSectionsImmediately() async {
    try {
      final sections = await _offlineService.getActiveSections();
      if (mounted) {
        setState(() {
          _sections = sections.cast<Section>();
        });
      }
      debugPrint('⚡ تم تحميل ${_sections.length} قسم فوراً');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام: $e');
    }
  }

  /// تحديث يدوي مع مزامنة من Firebase
  Future<void> _refreshSections() async {
    if (_isRefreshing) return;
    
    setState(() => _isRefreshing = true);
    
    try {
      // مزامنة مع Firebase
      await _offlineService.manualSync();
      
      // إعادة تحميل البيانات المحدثة
      await _loadSectionsImmediately();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث الأقسام: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الاختبارات - النظام الجديد'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          // عرض عدد الإشعارات غير المقروءة
          Consumer<UnifiedOfflineService>(
            builder: (context, service, child) {
              final unreadCount = service.unreadNotificationsCount;
              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications),
                    onPressed: () => _showNotifications(service),
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          '$unreadCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshSections,
        child: _buildContent(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showSystemStatus,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.info, color: Colors.white),
      ),
    );
  }

  Widget _buildContent() {
    // عرض البيانات فوراً بدون مؤشرات تحميل
    if (_sections.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // شريط المعلومات
        _buildInfoBar(),
        // قائمة الأقسام
        Expanded(
          child: _buildSectionsList(),
        ),
      ],
    );
  }

  Widget _buildInfoBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: Colors.blue.shade50,
      child: Consumer<UnifiedOfflineService>(
        builder: (context, service, child) {
          final hasSubscription = service.hasActiveSubscription();
          return Row(
            children: [
              Icon(
                hasSubscription ? Icons.verified : Icons.lock,
                color: hasSubscription ? Colors.green : Colors.orange,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  hasSubscription 
                    ? 'لديك اشتراك نشط - يمكنك الوصول لجميع المحتويات'
                    : 'قم بتفعيل اشتراكك للوصول للمحتويات المدفوعة',
                  style: TextStyle(
                    color: hasSubscription ? Colors.green.shade700 : Colors.orange.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (_isRefreshing)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionsList() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.85,
      ),
      itemCount: _sections.length,
      itemBuilder: (context, index) {
        final section = _sections[index];
        return _buildSectionCard(section);
      },
    );
  }

  Widget _buildSectionCard(Section section) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _navigateToSection(section),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(int.parse(section.color.replaceFirst('#', '0xFF'))),
                Color(int.parse(section.color.replaceFirst('#', '0xFF'))).withOpacity(0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة القسم
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.folder,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: 16),
              // اسم القسم
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  section.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 8),
              // وصف القسم
              if (section.description.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Text(
                    section.description,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              // مؤشر مجاني/مدفوع
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: section.isFree ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  section.isFree ? 'مجاني' : 'مدفوع',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أقسام متاحة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اسحب للأسفل للتحديث',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _refreshSections,
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث الآن'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSection(Section section) async {
    // تحميل المواد للقسم المحدد
    try {
      final subjects = await _offlineService.getActiveSubjectsBySection(section.id);
      
      if (mounted) {
        // الانتقال لصفحة المواد
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SubjectsBySectionPageExample(
              section: section,
              subjects: subjects.cast<Subject>(),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد القسم: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المواد: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showNotifications(UnifiedOfflineService service) {
    final notifications = service.notifications;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشعارات'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: notifications.isEmpty
            ? const Center(child: Text('لا توجد إشعارات'))
            : ListView.builder(
                itemCount: notifications.length,
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return ListTile(
                    leading: const Icon(Icons.notifications),
                    title: Text(notification.title),
                    subtitle: Text(notification.body),
                    trailing: Text(
                      '${notification.createdAt.hour}:${notification.createdAt.minute}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                },
              ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الأقسام'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'ادخل نص البحث...',
            prefixIcon: Icon(Icons.search),
          ),
          onSubmitted: (query) async {
            Navigator.pop(context);
            if (query.isNotEmpty) {
              final results = await _offlineService.searchSections(query);
              setState(() {
                _sections = results.cast<Section>();
              });
            } else {
              await _loadSectionsImmediately();
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showSystemStatus() {
    final status = _offlineService.systemStatus;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة النظام'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: status.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Expanded(child: Text('${entry.key}:')),
                  Text(
                    '${entry.value}',
                    style: TextStyle(
                      color: entry.value == true ? Colors.green : Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}

/// صفحة المواد (مثال مبسط)
class SubjectsBySectionPageExample extends StatelessWidget {
  final Section section;
  final List<Subject> subjects;

  const SubjectsBySectionPageExample({
    super.key,
    required this.section,
    required this.subjects,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(section.name),
        backgroundColor: Color(int.parse(section.color.replaceFirst('#', '0xFF'))),
        foregroundColor: Colors.white,
      ),
      body: subjects.isEmpty
        ? const Center(child: Text('لا توجد مواد في هذا القسم'))
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: subjects.length,
            itemBuilder: (context, index) {
              final subject = subjects[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Color(int.parse(subject.color.replaceFirst('#', '0xFF'))),
                    child: const Icon(Icons.book, color: Colors.white),
                  ),
                  title: Text(subject.name),
                  subtitle: Text(subject.description),
                  trailing: Icon(
                    subject.isFree ? Icons.lock_open : Icons.lock,
                    color: subject.isFree ? Colors.green : Colors.orange,
                  ),
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('تم النقر على ${subject.name}')),
                    );
                  },
                ),
              );
            },
          ),
    );
  }
}
