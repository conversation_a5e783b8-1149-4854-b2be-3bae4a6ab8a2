import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_subject_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/unified_offline_service.dart';
import '../../../../shared/services/simple_data_service.dart';
import '../../../../shared/services/single_read_data_service.dart';
import 'video_units_page.dart';

/// صفحة مواد الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoSubjectsPage extends StatefulWidget {
  final String sectionId;
  final bool hasSubscription;
  final bool isFreeSection;

  const VideoSubjectsPage({
    super.key,
    required this.sectionId,
    required this.hasSubscription,
    required this.isFreeSection,
  });

  @override
  State<VideoSubjectsPage> createState() => _VideoSubjectsPageState();
}

class _VideoSubjectsPageState extends State<VideoSubjectsPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoSubject> _subjects = [];
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadVideoSubjectsImmediately();
  }

  /// تحميل فوري لمواد الفيديو من النظام الجديد
  Future<void> _loadVideoSubjectsImmediately() async {
    try {
      // استخدام SingleReadDataService بدلاً من الخدمة الوهمية
      final dataService = SingleReadDataService.instance;
      final subjects = dataService.getVideoSubjectsBySection(widget.sectionId);

      if (mounted) {
        setState(() {
          _subjects = subjects;
        });
        debugPrint(
          '⚡ تم تحميل ${_subjects.length} مادة فيديو فوراً من SingleReadDataService للقسم ${widget.sectionId}',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد الفيديو: $e');
    }
  }

  /// تحديث يدوي مع مزامنة (النظام الجديد)
  Future<void> _refreshVideoSubjects() async {
    if (_isRefreshing) return;

    setState(() => _isRefreshing = true);

    try {
      await _offlineService.manualSync();
      await _loadVideoSubjectsImmediately();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث مواد الفيديو: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'المواد',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshVideoSubjects,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة مع إمكانية التحديث
    return RefreshIndicator(
      onRefresh: _refreshVideoSubjects,
      child: _subjects.isNotEmpty
          ? _buildSubjectsContent()
          : _buildEmptyState(),
    );
  }

  Widget _buildSubjectsContent() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20.h),
          Expanded(child: _buildSubjectsList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.subject, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مواد الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر المادة لمشاهدة الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectsList() {
    return ListView.builder(
      itemCount: _subjects.length,
      itemBuilder: (context, index) {
        final subject = _subjects[index];
        return _buildSubjectCard(subject);
      },
    );
  }

  Widget _buildSubjectCard(VideoSubject subject) {
    final isSubscribed = SubscriptionService.instance
        .isSubscribedToVideoSubject(subject.id);
    // إذا كان القسم مجاني، فالوصول متاح
    final hasAccess = widget.isFreeSection || isSubscribed;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToUnits(subject, hasAccess),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildSubjectIcon(subject, hasAccess),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              subject.name,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: hasAccess
                                    ? AppTheme.textPrimaryColor
                                    : AppTheme.textSecondaryColor,
                              ),
                            ),
                          ),
                          if (!hasAccess) ...[
                            SizedBox(width: 8.w),
                            Icon(Icons.lock, color: Colors.red, size: 20.sp),
                          ],
                        ],
                      ),
                      if (subject.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          subject.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: hasAccess
                                ? AppTheme.textSecondaryColor
                                : AppTheme.textSecondaryColor.withValues(
                                    alpha: 0.6,
                                  ),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      if (!hasAccess) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'يتطلب اشتراك',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  hasAccess ? Icons.arrow_forward_ios : Icons.lock_outline,
                  color: hasAccess ? AppTheme.textSecondaryColor : Colors.red,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubjectIcon(VideoSubject subject, bool hasAccess) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: hasAccess
            ? _getSubjectGradient(subject.color)
            : LinearGradient(
                colors: [Colors.grey.shade400, Colors.grey.shade600],
              ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(
        hasAccess ? Icons.video_collection : Icons.video_collection_outlined,
        color: Colors.white,
        size: 24.sp,
      ),
    );
  }

  LinearGradient _getSubjectGradient(String color) {
    return AppTheme.primaryGradient;
  }

  void _navigateToUnits(VideoSubject subject, bool hasAccess) {
    if (!hasAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تفعيل الاشتراك للوصول إلى هذه المادة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoUnitsPage(
          subjectId: subject.id,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_collection_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة المواد قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
