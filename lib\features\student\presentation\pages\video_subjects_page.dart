import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_subject_model.dart';
import '../../../../shared/services/subscription_service.dart';
import '../../../../shared/services/data_distribution_service.dart';
import '../../../../shared/services/optimized_unified_data_service.dart'
    as optimized_service;
// النظام الجديد Offline-First
import '../../../../shared/services/single_read_data_service.dart';
import 'video_units_page.dart';

/// صفحة مواد الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoSubjectsPage extends StatefulWidget {
  final String sectionId;
  final bool hasSubscription;
  final bool isFreeSection;

  const VideoSubjectsPage({
    super.key,
    required this.sectionId,
    required this.hasSubscription,
    required this.isFreeSection,
  });

  @override
  State<VideoSubjectsPage> createState() => _VideoSubjectsPageState();
}

class _VideoSubjectsPageState extends State<VideoSubjectsPage> {
  final DataDistributionService _dataService = DataDistributionService.instance;
  List<VideoSubject> _subjects = [];

  @override
  void initState() {
    super.initState();
    _loadVideoSubjectsImmediately();
  }

  /// تحميل فوري لمواد الفيديو من النظام الجديد
  Future<void> _loadVideoSubjectsImmediately() async {
    try {
      // استخدام خدمة فرز البيانات الجديدة
      final subjects = widget.isFreeSection
          ? _dataService.getFreeVideoSubjectsBySection(widget.sectionId)
          : _dataService.getPaidVideoSubjectsBySection(widget.sectionId);

      if (mounted) {
        setState(() {
          _subjects = subjects;
        });
        debugPrint(
          '⚡ تم تحميل ${_subjects.length} مادة فيديو فوراً من خدمة فرز البيانات للقسم ${widget.sectionId}',
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مواد الفيديو: $e');
    }
  }

  // تم إزالة وظيفة التحديث - التحديث يتم من الصفحة الرئيسية فقط

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'المواد',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: widget.isFreeSection
          ? [
              IconButton(
                icon: const Icon(Icons.download, color: Colors.white),
                onPressed: _showFreeVideoContentActivationDialog,
                tooltip: 'تفعيل نسخة مجانية',
              ),
            ]
          : null,
      // تم إزالة زر التحديث - التحديث يتم من الصفحة الرئيسية فقط
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة بدون تحديث
    return _subjects.isNotEmpty ? _buildSubjectsContent() : _buildEmptyState();
  }

  Widget _buildSubjectsContent() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: 20.h),
          Expanded(child: _buildSubjectsList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.subject, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مواد الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر المادة لمشاهدة الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubjectsList() {
    return ListView.builder(
      itemCount: _subjects.length,
      itemBuilder: (context, index) {
        final subject = _subjects[index];
        return _buildSubjectCard(subject);
      },
    );
  }

  Widget _buildSubjectCard(VideoSubject subject) {
    final isSubscribed = SubscriptionService.instance
        .isSubscribedToVideoSubject(subject.id);
    // إذا كان القسم مجاني، فالوصول متاح
    final hasAccess = widget.isFreeSection || isSubscribed;

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToUnits(subject, hasAccess),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildSubjectIcon(subject, hasAccess),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              subject.name,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: hasAccess
                                    ? AppTheme.textPrimaryColor
                                    : AppTheme.textSecondaryColor,
                              ),
                            ),
                          ),
                          if (!hasAccess) ...[
                            SizedBox(width: 8.w),
                            Icon(Icons.lock, color: Colors.red, size: 20.sp),
                          ],
                        ],
                      ),
                      if (subject.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          subject.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: hasAccess
                                ? AppTheme.textSecondaryColor
                                : AppTheme.textSecondaryColor.withValues(
                                    alpha: 0.6,
                                  ),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      if (!hasAccess) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'يتطلب اشتراك',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  hasAccess ? Icons.arrow_forward_ios : Icons.lock_outline,
                  color: hasAccess ? AppTheme.textSecondaryColor : Colors.red,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubjectIcon(VideoSubject subject, bool hasAccess) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: hasAccess
            ? _getSubjectGradient(subject.color)
            : LinearGradient(
                colors: [Colors.grey.shade400, Colors.grey.shade600],
              ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(
        hasAccess ? Icons.video_collection : Icons.video_collection_outlined,
        color: Colors.white,
        size: 24.sp,
      ),
    );
  }

  LinearGradient _getSubjectGradient(String color) {
    return AppTheme.primaryGradient;
  }

  void _navigateToUnits(VideoSubject subject, bool hasAccess) {
    if (!hasAccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب تفعيل الاشتراك للوصول إلى هذه المادة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoUnitsPage(
          subjectId: subject.id,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.video_collection_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد مواد فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة المواد قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تفعيل النسخة المجانية للفيديوهات
  void _showFreeVideoContentActivationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفعيل نسخة مجانية للفيديوهات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر مواد الفيديو التي تريد تحميلها:'),
            const SizedBox(height: 16),
            // قائمة مواد الفيديو المجانية للاختيار
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _subjects.length,
                itemBuilder: (context, index) {
                  final subject = _subjects[index];
                  return CheckboxListTile(
                    title: Text(subject.name),
                    value: false, // TODO: تتبع الاختيارات
                    onChanged: (value) {
                      // TODO: تطبيق منطق الاختيار
                    },
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _activateFreeVideoContent();
            },
            child: const Text('تفعيل'),
          ),
        ],
      ),
    );
  }

  /// تفعيل محتوى الفيديو المجاني المختار
  Future<void> _activateFreeVideoContent() async {
    try {
      // TODO: جمع مواد الفيديو المختارة
      final selectedSubjectIds = _subjects.map((s) => s.id).toList();

      // تحميل مواد الفيديو المجانية
      final success = await optimized_service
          .OptimizedUnifiedDataService
          .instance
          .loadSelectedFreeSubjects(
            selectedSubjectIds,
            true,
          ); // true للفيديوهات

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? '✅ تم تفعيل النسخة المجانية للفيديوهات بنجاح'
                  : '❌ فشل في تفعيل النسخة المجانية للفيديوهات',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تفعيل النسخة المجانية للفيديوهات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ حدث خطأ في تفعيل النسخة المجانية للفيديوهات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
