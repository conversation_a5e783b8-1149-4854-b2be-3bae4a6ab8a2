import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:video_player/video_player.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_model.dart';
import '../../../../shared/services/video_download_service.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/encryption_service.dart';
import '../../../../shared/services/screen_protection_service.dart';
import '../../../../shared/utils/video_player_wrapper.dart';

/// مشغل فيديو متطور وعصري مع جميع المزايا المطلوبة
class ModernVideoPlayer extends StatefulWidget {
  final Video video;
  final bool hasSubscription;
  final bool isOfflineMode;
  final VideoQuality? preferredQuality;

  const ModernVideoPlayer({
    super.key,
    required this.video,
    required this.hasSubscription,
    this.isOfflineMode = false,
    this.preferredQuality,
  });

  @override
  State<ModernVideoPlayer> createState() => _ModernVideoPlayerState();
}

class _ModernVideoPlayerState extends State<ModernVideoPlayer>
    with TickerProviderStateMixin {
  VideoPlayerController? _controller;
  final VideoDownloadService _downloadService = VideoDownloadService.instance;
  final VideoService _videoService = VideoService.instance;
  final EncryptionService _encryptionService = EncryptionService.instance;
  final ScreenProtectionService _protectionService =
      ScreenProtectionService.instance;

  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  bool _isLoading = true;
  double _playbackSpeed = 1.0;

  // متغيرات الحماية
  Timer? _recordingCheckTimer;
  bool _isRecordingDetected = false;

  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeVideo();
    _hideControlsAfterDelay();
    _startRecordingProtection();
  }

  void _setupAnimations() {
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controlsAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    _controlsAnimationController.forward();
  }

  /// بدء حماية مستمرة ضد تسجيل الشاشة
  void _startRecordingProtection() async {
    try {
      // تطبيق الحماية الأساسية
      await _protectionService.applyVideoProtection();

      // بدء الفحص المستمر كل 3 ثوانٍ
      _recordingCheckTimer = Timer.periodic(const Duration(seconds: 3), (
        timer,
      ) async {
        final isRecording = await _protectionService
            .performContinuousRecordingCheck();

        if (isRecording && !_isRecordingDetected) {
          setState(() {
            _isRecordingDetected = true;
          });

          // إيقاف تشغيل الفيديو فوراً
          if (_controller?.value.isPlaying == true) {
            await _controller?.pause();
          }

          // إظهار تحذير للمستخدم
          _showRecordingDetectedDialog();
        }
      });

      debugPrint('🛡️ تم بدء الحماية المستمرة ضد تسجيل الشاشة');
    } catch (e) {
      debugPrint('❌ خطأ في بدء حماية التسجيل: $e');
    }
  }

  /// إظهار تحذير عند اكتشاف تسجيل الشاشة
  void _showRecordingDetectedDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.red),
            SizedBox(width: 8),
            Text('تحذير أمني'),
          ],
        ),
        content: const Text(
          'تم اكتشاف محاولة تسجيل للشاشة!\n'
          'لحماية حقوق الطبع والنشر، تم إيقاف تشغيل الفيديو.\n'
          'يرجى إغلاق برنامج تسجيل الشاشة والمحاولة مرة أخرى.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // العودة للصفحة السابقة
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _initializeVideo() async {
    try {
      String? videoUrl;

      // إذا كان في وضع التشغيل المحلي، استخدم الملف المحلي فقط
      if (widget.isOfflineMode) {
        final quality = widget.preferredQuality ?? VideoQuality.quality480;
        final decryptedVideoPath = await _videoService
            .getDecryptedVideoForPlayback(widget.video.id, quality);

        if (decryptedVideoPath != null) {
          videoUrl = decryptedVideoPath;
          debugPrint('🎥 تشغيل الفيديو المحلي (وضع أوفلاين): $videoUrl');
        } else {
          throw Exception('الفيديو غير متاح للتشغيل المحلي');
        }
      } else {
        // التحقق من وجود الفيديو محلياً أولاً
        final isDownloaded = _downloadService.isVideoDownloaded(
          widget.video.id,
        );
        if (isDownloaded) {
          videoUrl = await _downloadService.getLocalVideoPath(widget.video.id);
          debugPrint('🎥 تشغيل الفيديو من التخزين المحلي: $videoUrl');
        } else {
          // تشغيل من الرابط المشفر
          videoUrl = await _getSecureVideoUrl();
          debugPrint('🌐 تشغيل الفيديو من الشبكة: $videoUrl');
        }
      }

      if (videoUrl != null && videoUrl.isNotEmpty) {
        // إنشاء controller باستخدام wrapper
        final isLocalFile = !videoUrl.startsWith('http');
        _controller = VideoPlayerWrapper.createController(
          videoPath: isLocalFile ? videoUrl : null,
          networkUrl: isLocalFile ? null : videoUrl,
          isLocalFile: isLocalFile,
        );

        await _controller!.initialize();

        if (mounted) {
          setState(() {
            _isInitialized = true;
            _isLoading = false;
          });

          _controller!.addListener(_videoListener);
        }
      } else {
        throw Exception('لا يوجد رابط فيديو متاح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الفيديو: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        // إظهار رسالة خطأ مناسبة حسب الوضع
        final errorMessage = widget.isOfflineMode
            ? 'الفيديو غير متاح للتشغيل المحلي'
            : 'تأكد من الاتصال بالانترنت';
        _showErrorDialog(errorMessage);
      }
    }
  }

  Future<String?> _getSecureVideoUrl() async {
    try {
      // الحصول على الرابط المشفر (أفضل جودة متاحة)
      final encryptedUrl =
          widget.video.encryptedUrl720 ??
          widget.video.encryptedUrl480 ??
          widget.video.encryptedUrl360;

      if (encryptedUrl == null || encryptedUrl.isEmpty) {
        throw Exception('لا يوجد رابط فيديو متاح');
      }

      debugPrint('🔓 فك تشفير رابط الفيديو: ${widget.video.title}');
      debugPrint('🔗 الرابط المشفر: ${encryptedUrl.substring(0, 50)}...');

      // فك تشفير الرابط باستخدام خدمة التشفير المتقدمة
      final decryptedUrl = await _encryptionService.decryptText(encryptedUrl);

      if (decryptedUrl.isEmpty) {
        throw Exception('فشل في فك تشفير رابط الفيديو');
      }

      debugPrint('✅ تم فك تشفير الرابط بنجاح مع الحماية المتقدمة');
      debugPrint('🌐 الرابط المفكوك: ${decryptedUrl.substring(0, 50)}...');
      return decryptedUrl;
    } catch (e) {
      debugPrint('❌ خطأ في فك تشفير رابط الفيديو: $e');
      return null;
    }
  }

  void _videoListener() {
    if (_controller != null && mounted) {
      final isPlaying = _controller!.value.isPlaying;
      if (isPlaying != _isPlaying) {
        setState(() {
          _isPlaying = isPlaying;
        });
      }
    }
  }

  void _togglePlayPause() {
    if (_controller != null && _isInitialized) {
      if (_isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
      _showControlsTemporarily();
    }
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _controlsAnimationController.forward();
      _hideControlsAfterDelay();
    } else {
      _controlsAnimationController.reverse();
    }
  }

  void _showControlsTemporarily() {
    setState(() {
      _showControls = true;
    });
    _controlsAnimationController.forward();
    _hideControlsAfterDelay();
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }
    _showControlsTemporarily();
  }

  void _changePlaybackSpeed() {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    final currentIndex = speeds.indexOf(_playbackSpeed);
    final nextIndex = (currentIndex + 1) % speeds.length;

    setState(() {
      _playbackSpeed = speeds[nextIndex];
    });

    if (_controller != null) {
      _controller!.setPlaybackSpeed(_playbackSpeed);
    }

    _showControlsTemporarily();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سرعة التشغيل: ${_playbackSpeed}x'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _seekTo(Duration position) {
    if (_controller != null) {
      _controller!.seekTo(position);
      _showControlsTemporarily();
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            _buildVideoPlayer(),
            if (_showControls) _buildControls(),
            if (_isLoading) _buildLoadingIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: _isInitialized && _controller != null
            ? AspectRatio(
                aspectRatio: _controller!.value.aspectRatio,
                child: VideoPlayer(_controller!),
              )
            : const Center(
                child: Icon(
                  Icons.video_library,
                  size: 100,
                  color: Colors.white54,
                ),
              ),
      ),
    );
  }

  Widget _buildControls() {
    return AnimatedBuilder(
      animation: _controlsAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.7),
                  Colors.transparent,
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
            child: Column(
              children: [
                _buildTopControls(),
                const Spacer(),
                _buildCenterControls(),
                const Spacer(),
                _buildBottomControls(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(Icons.arrow_back, color: Colors.white, size: 24.sp),
          ),
          Expanded(
            child: Text(
              widget.video.title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: _toggleFullscreen,
            icon: Icon(
              _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
              color: Colors.white,
              size: 24.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          onPressed: () {
            if (_controller != null) {
              final currentPosition = _controller!.value.position;
              final newPosition = currentPosition - const Duration(seconds: 10);
              _seekTo(newPosition);
            }
          },
          icon: Icon(Icons.replay_10, color: Colors.white, size: 32.sp),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 48.sp,
            ),
          ),
        ),
        IconButton(
          onPressed: () {
            if (_controller != null) {
              final currentPosition = _controller!.value.position;
              final newPosition = currentPosition + const Duration(seconds: 10);
              _seekTo(newPosition);
            }
          },
          icon: Icon(Icons.forward_10, color: Colors.white, size: 32.sp),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    if (!_isInitialized || _controller == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildProgressBar(),
          SizedBox(height: 8.h),
          Row(
            children: [
              Text(
                _formatDuration(_controller!.value.position),
                style: TextStyle(color: Colors.white, fontSize: 12.sp),
              ),
              const Spacer(),
              TextButton(
                onPressed: _changePlaybackSpeed,
                child: Text(
                  '${_playbackSpeed}x',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Text(
                _formatDuration(_controller!.value.duration),
                style: TextStyle(color: Colors.white, fontSize: 12.sp),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar() {
    return VideoProgressIndicator(
      _controller!,
      allowScrubbing: true,
      colors: VideoProgressColors(
        playedColor: AppTheme.primaryColor,
        bufferedColor: Colors.white.withValues(alpha: 0.3),
        backgroundColor: Colors.white.withValues(alpha: 0.1),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    // إيقاف الفحص المستمر
    _recordingCheckTimer?.cancel();

    // إزالة الحماية
    _protectionService.disableProtection();

    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    _controlsAnimationController.dispose();

    // إعادة تعيين اتجاه الشاشة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    super.dispose();
  }
}
