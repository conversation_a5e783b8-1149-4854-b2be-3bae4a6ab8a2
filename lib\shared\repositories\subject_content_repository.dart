import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/question_model.dart';
import '../services/persistent_storage_service.dart';
import '../services/subscription_service.dart';
import '../services/local_notifications_service.dart';

/// Repository لمحتوى المواد (وحدات، دروس، أسئلة) مع دعم Offline-First
/// يحمل المحتوى فقط للمواد المشترك فيها
class SubjectContentRepository extends ChangeNotifier {
  static final SubjectContentRepository _instance =
      SubjectContentRepository._internal();
  static SubjectContentRepository get instance => _instance;
  SubjectContentRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final LocalNotificationsService _notificationsService =
      LocalNotificationsService.instance;

  /// تحميل محتوى مادة معينة (وحدات، دروس، أسئلة) دفعة واحدة
  Future<Map<String, dynamic>> loadSubjectContent(String subjectId) async {
    try {
      debugPrint('🔄 تحميل محتوى المادة: $subjectId');

      // التحقق من الاشتراك أولاً
      if (!_subscriptionService.isSubscribedToSubject(subjectId)) {
        debugPrint('❌ المستخدم غير مشترك في المادة: $subjectId');
        return {
          'units': <Unit>[],
          'lessons': <Lesson>[],
          'questions': <Question>[],
        };
      }

      // محاولة تحميل البيانات المحلية أولاً
      final localContent = await _loadLocalSubjectContent(subjectId);

      // التحقق من التحديثات في الخلفية
      _checkSubjectUpdatesInBackground(subjectId);

      return localContent;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل محتوى المادة: $e');
      return {
        'units': <Unit>[],
        'lessons': <Lesson>[],
        'questions': <Question>[],
      };
    }
  }

  /// تحميل البيانات المحلية لمادة معينة
  Future<Map<String, dynamic>> _loadLocalSubjectContent(
    String subjectId,
  ) async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // تحميل الوحدات
      final unitsFile = File(
        '${storageDir.path}/subject_${subjectId}_units.json',
      );
      List<Unit> units = [];
      if (await unitsFile.exists()) {
        final unitsContent = await unitsFile.readAsString();
        final List<dynamic> unitsData = jsonDecode(unitsContent);
        units = unitsData.map((item) => Unit.fromMap(item)).toList();
      }

      // تحميل الدروس
      final lessonsFile = File(
        '${storageDir.path}/subject_${subjectId}_lessons.json',
      );
      List<Lesson> lessons = [];
      if (await lessonsFile.exists()) {
        final lessonsContent = await lessonsFile.readAsString();
        final List<dynamic> lessonsData = jsonDecode(lessonsContent);
        lessons = lessonsData.map((item) => Lesson.fromMap(item)).toList();
      }

      // تحميل الأسئلة
      final questionsFile = File(
        '${storageDir.path}/subject_${subjectId}_questions.json',
      );
      List<Question> questions = [];
      if (await questionsFile.exists()) {
        final questionsContent = await questionsFile.readAsString();
        final List<dynamic> questionsData = jsonDecode(questionsContent);
        questions = questionsData
            .map((item) => Question.fromMap(item))
            .toList();
      }

      debugPrint('📱 تم تحميل محتوى المادة $subjectId محلياً:');
      debugPrint('   - ${units.length} وحدة');
      debugPrint('   - ${lessons.length} درس');
      debugPrint('   - ${questions.length} سؤال');

      return {'units': units, 'lessons': lessons, 'questions': questions};
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحلية للمادة $subjectId: $e');
      return {
        'units': <Unit>[],
        'lessons': <Lesson>[],
        'questions': <Question>[],
      };
    }
  }

  /// حفظ محتوى المادة محلياً
  Future<void> _saveSubjectContentLocally(
    String subjectId,
    List<Unit> units,
    List<Lesson> lessons,
    List<Question> questions,
  ) async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // حفظ الوحدات
      final unitsFile = File(
        '${storageDir.path}/subject_${subjectId}_units.json',
      );
      final unitsData = units.map((unit) => unit.toMap()).toList();
      await unitsFile.writeAsString(jsonEncode(unitsData));

      // حفظ الدروس
      final lessonsFile = File(
        '${storageDir.path}/subject_${subjectId}_lessons.json',
      );
      final lessonsData = lessons.map((lesson) => lesson.toMap()).toList();
      await lessonsFile.writeAsString(jsonEncode(lessonsData));

      // حفظ الأسئلة
      final questionsFile = File(
        '${storageDir.path}/subject_${subjectId}_questions.json',
      );
      final questionsData = questions
          .map((question) => question.toMap())
          .toList();
      await questionsFile.writeAsString(jsonEncode(questionsData));

      // حفظ وقت التحديث
      await _persistentStorage.setLastUpdate(
        'subject_content_$subjectId',
        DateTime.now(),
      );

      debugPrint('✅ تم حفظ محتوى المادة $subjectId محلياً:');
      debugPrint('   - ${units.length} وحدة');
      debugPrint('   - ${lessons.length} درس');
      debugPrint('   - ${questions.length} سؤال');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ محتوى المادة محلياً: $e');
    }
  }

  /// التحقق من تحديثات المادة في الخلفية
  void _checkSubjectUpdatesInBackground(String subjectId) {
    Future.delayed(Duration.zero, () async {
      try {
        if (await _hasSubjectUpdates(subjectId)) {
          debugPrint(
            '🔄 يوجد تحديثات جديدة للمادة $subjectId، بدء المزامنة...',
          );
          await _fetchAndCacheSubjectContent(subjectId);
        }
      } catch (e) {
        debugPrint('❌ خطأ في التحقق من تحديثات المادة $subjectId: $e');
      }
    });
  }

  /// التحقق من وجود تحديثات للمادة
  Future<bool> _hasSubjectUpdates(String subjectId) async {
    try {
      final lastLocalUpdate = await _persistentStorage.getLastUpdate(
        'subject_content_$subjectId',
      );
      if (lastLocalUpdate == null) return true;

      // التحقق من آخر تحديث في Firebase
      final metadataDoc = await _firestore
          .collection('subject_metadata')
          .doc(subjectId)
          .get();

      if (!metadataDoc.exists) {
        debugPrint('❌ لا توجد بيانات وصفية للمادة: $subjectId');
        return false;
      }

      final remoteLastUpdate = (metadataDoc.data()!['lastUpdate'] as Timestamp)
          .toDate();
      final hasUpdates = remoteLastUpdate.isAfter(lastLocalUpdate);

      debugPrint('🔍 التحقق من تحديثات المادة: $subjectId');
      debugPrint('📅 آخر تحديث محلي: $lastLocalUpdate');
      debugPrint('📅 آخر تحديث بعيد: $remoteLastUpdate');
      debugPrint('🔄 يوجد تحديثات: $hasUpdates');

      return hasUpdates;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من تحديثات المادة: $e');
      return false;
    }
  }

  /// جلب وحفظ محتوى المادة من Firebase
  Future<Map<String, dynamic>> _fetchAndCacheSubjectContent(
    String subjectId,
  ) async {
    try {
      debugPrint('🔄 جلب محتوى المادة من Firebase: $subjectId');

      // تحميل المحتوى القديم للمقارنة
      final oldContent = await _loadLocalSubjectContent(subjectId);

      // جلب الوحدات
      final unitsSnapshot = await _firestore
          .collection('units')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final units = unitsSnapshot.docs
          .map((doc) => Unit.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      // جلب الدروس
      final lessonsSnapshot = await _firestore
          .collection('lessons')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('order')
          .get();

      final lessons = lessonsSnapshot.docs
          .map((doc) => Lesson.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      // جلب الأسئلة
      final questionsSnapshot = await _firestore
          .collection('questions')
          .where('subjectId', isEqualTo: subjectId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt')
          .get();

      final questions = questionsSnapshot.docs
          .map((doc) => Question.fromMap({...doc.data(), 'id': doc.id}))
          .toList();

      final newContent = {
        'units': units,
        'lessons': lessons,
        'questions': questions,
      };

      // إنشاء إشعار إذا كان هناك تغييرات
      await _createUpdateNotificationIfNeeded(
        subjectId,
        oldContent,
        newContent,
      );

      // حفظ البيانات محلياً
      await _saveSubjectContentLocally(subjectId, units, lessons, questions);

      debugPrint('✅ تم جلب وحفظ محتوى المادة $subjectId:');
      debugPrint('   - ${units.length} وحدة');
      debugPrint('   - ${lessons.length} درس');
      debugPrint('   - ${questions.length} سؤال');

      notifyListeners();

      return newContent;
    } catch (e) {
      debugPrint('❌ خطأ في جلب محتوى المادة من Firebase: $e');
      return await _loadLocalSubjectContent(subjectId);
    }
  }

  /// إنشاء إشعار تحديث إذا كان هناك تغييرات
  Future<void> _createUpdateNotificationIfNeeded(
    String subjectId,
    Map<String, dynamic> oldContent,
    Map<String, dynamic> newContent,
  ) async {
    try {
      // الحصول على اسم المادة
      final subjects = _subscriptionService.subscribedSubjects;
      final subject = subjects.firstWhere(
        (s) => s.id == subjectId,
        orElse: () => throw Exception('المادة غير موجودة'),
      );

      await _notificationsService.createContentUpdateNotification(
        subjectId: subjectId,
        subjectName: subject.name,
        oldContent: oldContent,
        newContent: newContent,
      );
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار التحديث: $e');
    }
  }

  /// الحصول على وحدات مادة معينة
  Future<List<Unit>> getSubjectUnits(String subjectId) async {
    final content = await loadSubjectContent(subjectId);
    return content['units'] as List<Unit>;
  }

  /// الحصول على دروس مادة معينة
  Future<List<Lesson>> getSubjectLessons(String subjectId) async {
    final content = await loadSubjectContent(subjectId);
    return content['lessons'] as List<Lesson>;
  }

  /// الحصول على أسئلة مادة معينة
  Future<List<Question>> getSubjectQuestions(String subjectId) async {
    final content = await loadSubjectContent(subjectId);
    return content['questions'] as List<Question>;
  }

  /// الحصول على دروس وحدة معينة
  Future<List<Lesson>> getUnitLessons(String unitId) async {
    final content = await loadSubjectContent(''); // سنحتاج لتحسين هذا
    final lessons = content['lessons'] as List<Lesson>;
    return lessons.where((lesson) => lesson.unitId == unitId).toList();
  }

  /// الحصول على أسئلة وحدة معينة
  Future<List<Question>> getUnitQuestions(String unitId) async {
    final content = await loadSubjectContent(''); // سنحتاج لتحسين هذا
    final questions = content['questions'] as List<Question>;
    return questions.where((question) => question.unitId == unitId).toList();
  }

  /// الحصول على أسئلة درس معين
  Future<List<Question>> getLessonQuestions(String lessonId) async {
    final content = await loadSubjectContent(''); // سنحتاج لتحسين هذا
    final questions = content['questions'] as List<Question>;
    return questions
        .where((question) => question.lessonId == lessonId)
        .toList();
  }

  /// مزامنة جميع المواد المشترك فيها
  Future<void> syncAllSubscribedSubjects() async {
    try {
      final subscribedSubjects = _subscriptionService.subscribedSubjects;
      debugPrint('🔄 مزامنة ${subscribedSubjects.length} مادة مشترك فيها...');

      for (final subject in subscribedSubjects) {
        await _fetchAndCacheSubjectContent(subject.id);
      }

      debugPrint('✅ تم مزامنة جميع المواد المشترك فيها');
    } catch (e) {
      debugPrint('❌ خطأ في مزامنة المواد المشترك فيها: $e');
    }
  }

  /// تنظيف بيانات المواد غير المشترك فيها
  Future<void> cleanupUnsubscribedSubjects() async {
    try {
      final subscribedSubjectIds = _subscriptionService.subscribedSubjects
          .map((subject) => subject.id)
          .toSet();

      final storageDir = _persistentStorage.storageDirectory;
      final files = storageDir.listSync();

      for (final file in files) {
        if (file is File &&
            file.path.contains('subject_') &&
            file.path.contains('_')) {
          final fileName = file.path.split('/').last;
          final subjectId = fileName.split('_')[1];

          if (!subscribedSubjectIds.contains(subjectId)) {
            await file.delete();
            debugPrint('🗑️ تم حذف بيانات المادة غير المشترك فيها: $subjectId');
          }
        }
      }

      debugPrint('✅ تم تنظيف بيانات المواد غير المشترك فيها');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
    }
  }
}
