import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/question_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../models/user_subscription_model.dart';
import '../../core/models/section.dart';
import 'device_service.dart';
import 'persistent_storage_service.dart';

/// خدمة القراءة الواحدة - تحمل جميع البيانات بقراءة واحدة فقط
/// هذه الخدمة تضمن عدم تجاوز قراءة واحدة يومياً للطالب الواحد
class SingleReadDataService extends ChangeNotifier {
  static final SingleReadDataService _instance =
      SingleReadDataService._internal();
  static SingleReadDataService get instance => _instance;
  SingleReadDataService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DeviceService _deviceService = DeviceService.instance;
  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;

  // حالة التحميل
  bool _isLoading = false;
  double _progress = 0.0;
  String _currentStep = '';
  String? _error;

  // البيانات المحلية
  UserSubscription? _subscription;
  List<Section> _sections = [];
  List<Subject> _subjects = [];
  List<VideoSection> _videoSections = [];
  List<VideoSubject> _videoSubjects = [];
  List<Unit> _units = [];
  List<Lesson> _lessons = [];
  List<Question> _questions = [];
  List<VideoUnit> _videoUnits = [];
  List<VideoLesson> _videoLessons = [];
  List<Video> _videos = [];

  DateTime? _lastUpdateTime;

  // Getters للحالة
  bool get isLoading => _isLoading;
  double get progress => _progress;
  String get currentStep => _currentStep;
  String? get error => _error;
  DateTime? get lastUpdateTime => _lastUpdateTime;

  // Getters للبيانات
  UserSubscription? get subscription => _subscription;
  List<Section> get sections => _sections;
  List<Subject> get subjects => _subjects;
  List<VideoSection> get videoSections => _videoSections;
  List<VideoSubject> get videoSubjects => _videoSubjects;
  List<Unit> get units => _units;
  List<Lesson> get lessons => _lessons;
  List<Question> get questions => _questions;
  List<VideoUnit> get videoUnits => _videoUnits;
  List<VideoLesson> get videoLessons => _videoLessons;
  List<Video> get videos => _videos;

  /// تهيئة الخدمة وتحميل البيانات المحلية
  Future<void> initialize() async {
    try {
      await _persistentStorage.initialize();
      await _loadLocalData();
      debugPrint('✅ تم تهيئة خدمة القراءة الواحدة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة القراءة الواحدة: $e');
    }
  }

  /// التحديث اليدوي - القراءة الواحدة الوحيدة المسموحة
  Future<bool> performSingleRead() async {
    if (_isLoading) {
      debugPrint('⚠️ التحديث قيد التنفيذ بالفعل');
      return false;
    }

    try {
      _setLoading(true);
      _setError(null);

      debugPrint('🚀 بدء القراءة الواحدة الشاملة...');

      // الخطوة 1: الحصول على معرف الجهاز
      _updateProgress(0.1, 'الحصول على معرف الجهاز...');
      final deviceId = await _deviceService.getDeviceId();

      // الخطوة 2: قراءة واحدة شاملة من Firebase
      _updateProgress(0.2, 'تحميل جميع البيانات من الخادم...');
      final userData = await _performSingleFirebaseRead(deviceId);

      if (userData == null) {
        throw Exception('لا توجد بيانات للمستخدم');
      }

      // الخطوة 3: معالجة البيانات محلياً
      _updateProgress(0.5, 'معالجة البيانات...');
      await _processUserData(userData);

      // الخطوة 4: حفظ البيانات محلياً
      _updateProgress(0.8, 'حفظ البيانات محلياً...');
      await _saveAllDataLocally();

      // الخطوة 5: فهرسة البيانات
      _updateProgress(0.9, 'فهرسة البيانات...');
      _indexDataLocally();

      _updateProgress(1.0, 'تم الانتهاء بنجاح!');
      _lastUpdateTime = DateTime.now();

      debugPrint('✅ تم إنجاز القراءة الواحدة بنجاح');
      debugPrint('📊 إحصائيات البيانات المحملة:');
      debugPrint('   - الأقسام: ${_sections.length}');
      debugPrint('   - المواد: ${_subjects.length}');
      debugPrint('   - أقسام الفيديو: ${_videoSections.length}');
      debugPrint('   - مواد الفيديو: ${_videoSubjects.length}');
      debugPrint('   - الوحدات: ${_units.length}');
      debugPrint('   - الدروس: ${_lessons.length}');
      debugPrint('   - الأسئلة: ${_questions.length}');
      debugPrint('   - وحدات الفيديو: ${_videoUnits.length}');
      debugPrint('   - دروس الفيديو: ${_videoLessons.length}');
      debugPrint('   - الفيديوهات: ${_videos.length}');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في القراءة الواحدة: $e');
      _setError('فشل في تحميل البيانات: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تنفيذ القراءة الواحدة من Firebase - هذه هي القراءة الوحيدة المسموحة
  Future<Map<String, dynamic>?> _performSingleFirebaseRead(
    String deviceId,
  ) async {
    try {
      debugPrint('📡 تنفيذ القراءة الواحدة من Firebase للجهاز: $deviceId');

      // قراءة واحدة فقط من مجموعة user_data
      final docSnapshot = await _firestore
          .collection('user_data')
          .doc(deviceId)
          .get();

      if (!docSnapshot.exists) {
        debugPrint('⚠️ لا توجد بيانات للمستخدم، تحميل البيانات العامة...');
        return await _loadPublicData(deviceId);
      }

      final userData = docSnapshot.data()!;
      debugPrint('✅ تم تحميل بيانات المستخدم بقراءة واحدة');

      return userData;
    } catch (e) {
      debugPrint('❌ خطأ في القراءة من Firebase: $e');
      rethrow;
    }
  }

  /// تحميل البيانات العامة من المجموعات الأصلية
  Future<Map<String, dynamic>> _loadPublicData(String deviceId) async {
    try {
      debugPrint('📡 تحميل البيانات العامة من Firebase...');

      // تحميل الأقسام
      final sectionsSnapshot = await _firestore.collection('sections').get();
      final sections = sectionsSnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();

      // تحميل المواد
      final subjectsSnapshot = await _firestore.collection('subjects').get();
      final subjects = subjectsSnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();

      // تحميل أقسام الفيديو
      final videoSectionsSnapshot = await _firestore
          .collection('video_sections')
          .get();
      final videoSections = videoSectionsSnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();

      // تحميل مواد الفيديو
      final videoSubjectsSnapshot = await _firestore
          .collection('video_subjects')
          .get();
      final videoSubjects = videoSubjectsSnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();

      debugPrint('✅ تم تحميل البيانات العامة:');
      debugPrint('   - الأقسام: ${sections.length}');
      debugPrint('   - المواد: ${subjects.length}');
      debugPrint('   - أقسام الفيديو: ${videoSections.length}');
      debugPrint('   - مواد الفيديو: ${videoSubjects.length}');

      return {
        'subscription': {
          'isActive': false,
          'subjectIds': <String>[],
          'videoSubjectIds': <String>[],
          'activatedAt': null,
          'expiresAt': null,
        },
        'sections': sections,
        'subjects': subjects,
        'video_sections': videoSections,
        'video_subjects': videoSubjects,
        'units': <Map<String, dynamic>>[],
        'lessons': <Map<String, dynamic>>[],
        'questions': <Map<String, dynamic>>[],
        'video_units': <Map<String, dynamic>>[],
        'video_lessons': <Map<String, dynamic>>[],
        'videos': <Map<String, dynamic>>[],
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات العامة: $e');
      rethrow;
    }
  }

  /// معالجة بيانات المستخدم المحملة
  Future<void> _processUserData(Map<String, dynamic> userData) async {
    try {
      // معالجة الاشتراك
      final subscriptionData =
          userData['subscription'] as Map<String, dynamic>?;
      if (subscriptionData != null) {
        _subscription = UserSubscription.fromMap({
          ...subscriptionData,
          'deviceId': await _deviceService.getDeviceId(),
        });
      }

      // معالجة الأقسام
      final sectionsData = userData['sections'] as List<dynamic>? ?? [];
      _sections = sectionsData
          .map((data) => Section.fromLocalMap(data as Map<String, dynamic>))
          .toList();

      // معالجة المواد
      final subjectsData = userData['subjects'] as List<dynamic>? ?? [];
      _subjects = subjectsData
          .map((data) => Subject.fromLocalMap(data as Map<String, dynamic>))
          .toList();

      // معالجة أقسام الفيديو
      final videoSectionsData =
          userData['video_sections'] as List<dynamic>? ?? [];
      _videoSections = videoSectionsData
          .map(
            (data) => VideoSection.fromLocalMap(data as Map<String, dynamic>),
          )
          .toList();

      // معالجة مواد الفيديو
      final videoSubjectsData =
          userData['video_subjects'] as List<dynamic>? ?? [];
      _videoSubjects = videoSubjectsData
          .map(
            (data) => VideoSubject.fromLocalMap(data as Map<String, dynamic>),
          )
          .toList();

      // معالجة الوحدات
      final unitsData = userData['units'] as List<dynamic>? ?? [];
      _units = unitsData
          .map((data) => Unit.fromMap(data as Map<String, dynamic>))
          .toList();

      // معالجة الدروس
      final lessonsData = userData['lessons'] as List<dynamic>? ?? [];
      _lessons = lessonsData
          .map((data) => Lesson.fromMap(data as Map<String, dynamic>))
          .toList();

      // معالجة الأسئلة
      final questionsData = userData['questions'] as List<dynamic>? ?? [];
      _questions = questionsData
          .map((data) => Question.fromMap(data as Map<String, dynamic>))
          .toList();

      // معالجة وحدات الفيديو
      final videoUnitsData = userData['video_units'] as List<dynamic>? ?? [];
      _videoUnits = videoUnitsData
          .map((data) => VideoUnit.fromLocalMap(data as Map<String, dynamic>))
          .toList();

      // معالجة دروس الفيديو
      final videoLessonsData =
          userData['video_lessons'] as List<dynamic>? ?? [];
      _videoLessons = videoLessonsData
          .map((data) => VideoLesson.fromLocalMap(data as Map<String, dynamic>))
          .toList();

      // معالجة الفيديوهات
      final videosData = userData['videos'] as List<dynamic>? ?? [];
      _videos = videosData
          .map((data) => Video.fromLocalMap(data as Map<String, dynamic>))
          .toList();

      debugPrint('✅ تم معالجة جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في معالجة بيانات المستخدم: $e');
      rethrow;
    }
  }

  /// تحديث حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (!loading) {
      _progress = 0.0;
      _currentStep = '';
    }
    notifyListeners();
  }

  /// تحديث التقدم
  void _updateProgress(double progress, String step) {
    _progress = progress;
    _currentStep = step;
    debugPrint('📊 التقدم: ${(progress * 100).toInt()}% - $step');
    notifyListeners();
  }

  /// تعيين خطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// تحميل البيانات المحلية
  Future<void> _loadLocalData() async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // تحميل الاشتراك
      final subscriptionFile = File(
        '${storageDir.path}/user_subscription.json',
      );
      if (await subscriptionFile.exists()) {
        final content = await subscriptionFile.readAsString();
        final data = jsonDecode(content) as Map<String, dynamic>;
        _subscription = UserSubscription.fromMap(data);
      }

      // تحميل الأقسام
      final sectionsFile = File('${storageDir.path}/sections.json');
      if (await sectionsFile.exists()) {
        final content = await sectionsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _sections = data.map((item) => Section.fromLocalMap(item)).toList();
      }

      // تحميل المواد
      final subjectsFile = File('${storageDir.path}/subjects.json');
      if (await subjectsFile.exists()) {
        final content = await subjectsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _subjects = data.map((item) => Subject.fromLocalMap(item)).toList();
      }

      // تحميل أقسام الفيديو
      final videoSectionsFile = File('${storageDir.path}/video_sections.json');
      if (await videoSectionsFile.exists()) {
        final content = await videoSectionsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoSections = data
            .map((item) => VideoSection.fromLocalMap(item))
            .toList();
      }

      // تحميل مواد الفيديو
      final videoSubjectsFile = File('${storageDir.path}/video_subjects.json');
      if (await videoSubjectsFile.exists()) {
        final content = await videoSubjectsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoSubjects = data
            .map((item) => VideoSubject.fromLocalMap(item))
            .toList();
      }

      // تحميل الوحدات
      final unitsFile = File('${storageDir.path}/units.json');
      if (await unitsFile.exists()) {
        final content = await unitsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _units = data.map((item) => Unit.fromMap(item)).toList();
      }

      // تحميل الدروس
      final lessonsFile = File('${storageDir.path}/lessons.json');
      if (await lessonsFile.exists()) {
        final content = await lessonsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _lessons = data.map((item) => Lesson.fromMap(item)).toList();
      }

      // تحميل الأسئلة
      final questionsFile = File('${storageDir.path}/questions.json');
      if (await questionsFile.exists()) {
        final content = await questionsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _questions = data.map((item) => Question.fromMap(item)).toList();
      }

      // تحميل وحدات الفيديو
      final videoUnitsFile = File('${storageDir.path}/video_units.json');
      if (await videoUnitsFile.exists()) {
        final content = await videoUnitsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoUnits = data.map((item) => VideoUnit.fromLocalMap(item)).toList();
      }

      // تحميل دروس الفيديو
      final videoLessonsFile = File('${storageDir.path}/video_lessons.json');
      if (await videoLessonsFile.exists()) {
        final content = await videoLessonsFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videoLessons = data
            .map((item) => VideoLesson.fromLocalMap(item))
            .toList();
      }

      // تحميل الفيديوهات
      final videosFile = File('${storageDir.path}/videos.json');
      if (await videosFile.exists()) {
        final content = await videosFile.readAsString();
        final List<dynamic> data = jsonDecode(content);
        _videos = data.map((item) => Video.fromLocalMap(item)).toList();
      }

      // تحميل وقت آخر تحديث
      final lastUpdateFile = File('${storageDir.path}/last_update.txt');
      if (await lastUpdateFile.exists()) {
        final content = await lastUpdateFile.readAsString();
        _lastUpdateTime = DateTime.tryParse(content);
      }

      debugPrint('✅ تم تحميل البيانات المحلية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحلية: $e');
    }
  }

  /// حفظ جميع البيانات محلياً
  Future<void> _saveAllDataLocally() async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // حفظ الاشتراك
      if (_subscription != null) {
        final subscriptionFile = File(
          '${storageDir.path}/user_subscription.json',
        );
        await subscriptionFile.writeAsString(
          jsonEncode(_subscription!.toMap()),
        );
      }

      // حفظ الأقسام
      final sectionsFile = File('${storageDir.path}/sections.json');
      final sectionsData = _sections.map((item) => item.toLocalMap()).toList();
      await sectionsFile.writeAsString(jsonEncode(sectionsData));

      // حفظ المواد
      final subjectsFile = File('${storageDir.path}/subjects.json');
      final subjectsData = _subjects.map((item) => item.toLocalMap()).toList();
      await subjectsFile.writeAsString(jsonEncode(subjectsData));

      // حفظ أقسام الفيديو
      final videoSectionsFile = File('${storageDir.path}/video_sections.json');
      final videoSectionsData = _videoSections
          .map((item) => item.toLocalMap())
          .toList();
      await videoSectionsFile.writeAsString(jsonEncode(videoSectionsData));

      // حفظ مواد الفيديو
      final videoSubjectsFile = File('${storageDir.path}/video_subjects.json');
      final videoSubjectsData = _videoSubjects
          .map((item) => item.toLocalMap())
          .toList();
      await videoSubjectsFile.writeAsString(jsonEncode(videoSubjectsData));

      // حفظ الوحدات
      final unitsFile = File('${storageDir.path}/units.json');
      final unitsData = _units.map((item) => item.toMap()).toList();
      await unitsFile.writeAsString(jsonEncode(unitsData));

      // حفظ الدروس
      final lessonsFile = File('${storageDir.path}/lessons.json');
      final lessonsData = _lessons.map((item) => item.toMap()).toList();
      await lessonsFile.writeAsString(jsonEncode(lessonsData));

      // حفظ الأسئلة
      final questionsFile = File('${storageDir.path}/questions.json');
      final questionsData = _questions.map((item) => item.toMap()).toList();
      await questionsFile.writeAsString(jsonEncode(questionsData));

      // حفظ وحدات الفيديو
      final videoUnitsFile = File('${storageDir.path}/video_units.json');
      final videoUnitsData = _videoUnits
          .map((item) => item.toLocalMap())
          .toList();
      await videoUnitsFile.writeAsString(jsonEncode(videoUnitsData));

      // حفظ دروس الفيديو
      final videoLessonsFile = File('${storageDir.path}/video_lessons.json');
      final videoLessonsData = _videoLessons
          .map((item) => item.toLocalMap())
          .toList();
      await videoLessonsFile.writeAsString(jsonEncode(videoLessonsData));

      // حفظ الفيديوهات
      final videosFile = File('${storageDir.path}/videos.json');
      final videosData = _videos.map((item) => item.toLocalMap()).toList();
      await videosFile.writeAsString(jsonEncode(videosData));

      // حفظ وقت آخر تحديث
      final lastUpdateFile = File('${storageDir.path}/last_update.txt');
      await lastUpdateFile.writeAsString(DateTime.now().toIso8601String());

      debugPrint('✅ تم حفظ جميع البيانات محلياً بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات محلياً: $e');
      rethrow;
    }
  }

  /// فهرسة البيانات محلياً للوصول السريع
  void _indexDataLocally() {
    try {
      debugPrint('🔍 بدء فهرسة البيانات محلياً...');

      // فهرسة المواد حسب القسم
      final subjectsBySection = <String, List<Subject>>{};
      for (final subject in _subjects) {
        if (!subjectsBySection.containsKey(subject.sectionId)) {
          subjectsBySection[subject.sectionId] = [];
        }
        subjectsBySection[subject.sectionId]!.add(subject);
      }

      // فهرسة مواد الفيديو حسب القسم
      final videoSubjectsBySection = <String, List<VideoSubject>>{};
      for (final videoSubject in _videoSubjects) {
        if (!videoSubjectsBySection.containsKey(videoSubject.sectionId)) {
          videoSubjectsBySection[videoSubject.sectionId] = [];
        }
        videoSubjectsBySection[videoSubject.sectionId]!.add(videoSubject);
      }

      // فهرسة الوحدات حسب المادة
      final unitsBySubject = <String, List<Unit>>{};
      for (final unit in _units) {
        if (!unitsBySubject.containsKey(unit.subjectId)) {
          unitsBySubject[unit.subjectId] = [];
        }
        unitsBySubject[unit.subjectId]!.add(unit);
      }

      // فهرسة الدروس حسب الوحدة
      final lessonsByUnit = <String, List<Lesson>>{};
      for (final lesson in _lessons) {
        if (!lessonsByUnit.containsKey(lesson.unitId)) {
          lessonsByUnit[lesson.unitId] = [];
        }
        lessonsByUnit[lesson.unitId]!.add(lesson);
      }

      // فهرسة الأسئلة حسب الدرس
      final questionsByLesson = <String, List<Question>>{};
      for (final question in _questions) {
        if (!questionsByLesson.containsKey(question.lessonId)) {
          questionsByLesson[question.lessonId] = [];
        }
        questionsByLesson[question.lessonId]!.add(question);
      }

      // فهرسة وحدات الفيديو حسب المادة
      final videoUnitsBySubject = <String, List<VideoUnit>>{};
      for (final videoUnit in _videoUnits) {
        if (!videoUnitsBySubject.containsKey(videoUnit.subjectId)) {
          videoUnitsBySubject[videoUnit.subjectId] = [];
        }
        videoUnitsBySubject[videoUnit.subjectId]!.add(videoUnit);
      }

      // فهرسة دروس الفيديو حسب الوحدة
      final videoLessonsByUnit = <String, List<VideoLesson>>{};
      for (final videoLesson in _videoLessons) {
        if (!videoLessonsByUnit.containsKey(videoLesson.unitId)) {
          videoLessonsByUnit[videoLesson.unitId] = [];
        }
        videoLessonsByUnit[videoLesson.unitId]!.add(videoLesson);
      }

      // فهرسة الفيديوهات حسب الدرس
      final videosByLesson = <String, List<Video>>{};
      for (final video in _videos) {
        if (!videosByLesson.containsKey(video.lessonId)) {
          videosByLesson[video.lessonId] = [];
        }
        videosByLesson[video.lessonId]!.add(video);
      }

      debugPrint('✅ تم إنجاز فهرسة البيانات محلياً');
      debugPrint('📊 إحصائيات الفهرسة:');
      debugPrint('   - المواد حسب القسم: ${subjectsBySection.length} قسم');
      debugPrint(
        '   - مواد الفيديو حسب القسم: ${videoSubjectsBySection.length} قسم',
      );
      debugPrint('   - الوحدات حسب المادة: ${unitsBySubject.length} مادة');
      debugPrint('   - الدروس حسب الوحدة: ${lessonsByUnit.length} وحدة');
      debugPrint('   - الأسئلة حسب الدرس: ${questionsByLesson.length} درس');
      debugPrint(
        '   - وحدات الفيديو حسب المادة: ${videoUnitsBySubject.length} مادة',
      );
      debugPrint(
        '   - دروس الفيديو حسب الوحدة: ${videoLessonsByUnit.length} وحدة',
      );
      debugPrint('   - الفيديوهات حسب الدرس: ${videosByLesson.length} درس');
    } catch (e) {
      debugPrint('❌ خطأ في فهرسة البيانات: $e');
    }
  }

  // ==================== دوال الوصول للبيانات المفهرسة ====================

  /// الحصول على المواد حسب القسم
  List<Subject> getSubjectsBySection(String sectionId) {
    return _subjects
        .where((subject) => subject.sectionId == sectionId && subject.isActive)
        .toList();
  }

  /// الحصول على مواد الفيديو حسب القسم
  List<VideoSubject> getVideoSubjectsBySection(String sectionId) {
    return _videoSubjects
        .where(
          (videoSubject) =>
              videoSubject.sectionId == sectionId && videoSubject.isActive,
        )
        .toList();
  }

  /// الحصول على الوحدات حسب المادة
  List<Unit> getUnitsBySubject(String subjectId) {
    return _units
        .where((unit) => unit.subjectId == subjectId && unit.isActive)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الدروس حسب الوحدة
  List<Lesson> getLessonsByUnit(String unitId) {
    return _lessons
        .where((lesson) => lesson.unitId == unitId && lesson.isActive)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الأسئلة حسب الدرس
  List<Question> getQuestionsByLesson(String lessonId) {
    return _questions
        .where((question) => question.lessonId == lessonId && question.isActive)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على وحدات الفيديو حسب المادة
  List<VideoUnit> getVideoUnitsBySubject(String subjectId) {
    return _videoUnits
        .where(
          (videoUnit) => videoUnit.subjectId == subjectId && videoUnit.isActive,
        )
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على دروس الفيديو حسب الوحدة
  List<VideoLesson> getVideoLessonsByUnit(String unitId) {
    return _videoLessons
        .where(
          (videoLesson) => videoLesson.unitId == unitId && videoLesson.isActive,
        )
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الفيديوهات حسب الدرس
  List<Video> getVideosByLesson(String lessonId) {
    return _videos
        .where((video) => video.lessonId == lessonId && video.isActive)
        .toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  /// الحصول على الأسئلة حسب المادة
  List<Question> getQuestionsBySubject(String subjectId) {
    return _questions
        .where(
          (question) => question.subjectId == subjectId && question.isActive,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على الأسئلة حسب الوحدة
  List<Question> getQuestionsByUnit(String unitId) {
    return _questions
        .where((question) => question.unitId == unitId && question.isActive)
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على أسئلة الدورات حسب المادة
  List<Question> getCourseQuestionsBySubject(String subjectId) {
    return _questions
        .where(
          (question) =>
              question.subjectId == subjectId &&
              question.isActive &&
              question.isCourseQuestion,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على أسئلة الدورات حسب الوحدة
  List<Question> getCourseQuestionsByUnit(String unitId) {
    return _questions
        .where(
          (question) =>
              question.unitId == unitId &&
              question.isActive &&
              question.isCourseQuestion,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// الحصول على أسئلة الدورات حسب الدرس
  List<Question> getCourseQuestionsByLesson(String lessonId) {
    return _questions
        .where(
          (question) =>
              question.lessonId == lessonId &&
              question.isActive &&
              question.isCourseQuestion,
        )
        .toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  /// التحقق من الاشتراك في مادة
  bool isSubscribedToSubject(String subjectId) {
    return _subscription?.subscribedSubjectIds.contains(subjectId) ?? false;
  }

  /// التحقق من الاشتراك في مادة فيديو
  bool isSubscribedToVideoSubject(String videoSubjectId) {
    return _subscription?.videoSubjectIds.contains(videoSubjectId) ?? false;
  }

  /// الحصول على المواد المشترك بها فقط
  List<Subject> getSubscribedSubjects() {
    if (_subscription == null || !_subscription!.isActive) {
      return [];
    }
    return _subjects
        .where(
          (subject) =>
              _subscription!.subscribedSubjectIds.contains(subject.id) &&
              subject.isActive,
        )
        .toList();
  }

  /// الحصول على مواد الفيديو المشترك بها فقط
  List<VideoSubject> getSubscribedVideoSubjects() {
    if (_subscription == null || !_subscription!.isActive) {
      return [];
    }
    return _videoSubjects
        .where(
          (videoSubject) =>
              _subscription!.videoSubjectIds.contains(videoSubject.id) &&
              videoSubject.isActive,
        )
        .toList();
  }

  /// الحصول على الأقسام المجانية
  List<Section> getFreeTestSections() {
    return _sections
        .where((section) => section.isFree && section.isActive)
        .toList();
  }

  /// الحصول على الأقسام المدفوعة
  List<Section> getPaidTestSections() {
    return _sections
        .where((section) => !section.isFree && section.isActive)
        .toList();
  }

  /// الحصول على أقسام الفيديو المجانية
  List<VideoSection> getFreeVideoSections() {
    return _videoSections
        .where((section) => section.isFree && section.isActive)
        .toList();
  }

  /// الحصول على أقسام الفيديو المدفوعة
  List<VideoSection> getPaidVideoSections() {
    return _videoSections
        .where((section) => !section.isFree && section.isActive)
        .toList();
  }

  /// مسح جميع البيانات المحلية
  Future<void> clearAllLocalData() async {
    try {
      final storageDir = _persistentStorage.storageDirectory;

      // حذف جميع ملفات البيانات
      final files = [
        'user_subscription.json',
        'sections.json',
        'subjects.json',
        'video_sections.json',
        'video_subjects.json',
        'units.json',
        'lessons.json',
        'questions.json',
        'video_units.json',
        'video_lessons.json',
        'videos.json',
        'last_update.txt',
      ];

      for (final fileName in files) {
        final file = File('${storageDir.path}/$fileName');
        if (await file.exists()) {
          await file.delete();
        }
      }

      // مسح البيانات من الذاكرة
      _subscription = null;
      _sections.clear();
      _subjects.clear();
      _videoSections.clear();
      _videoSubjects.clear();
      _units.clear();
      _lessons.clear();
      _questions.clear();
      _videoUnits.clear();
      _videoLessons.clear();
      _videos.clear();
      _lastUpdateTime = null;

      debugPrint('✅ تم مسح جميع البيانات المحلية');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في مسح البيانات المحلية: $e');
    }
  }
}
