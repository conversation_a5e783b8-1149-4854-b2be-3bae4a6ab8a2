import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/subject_model.dart';
import '../services/simple_data_service.dart';
import 'base_repository.dart';

/// Repository للمواد مع دعم Offline-First
class SubjectsRepository extends BaseRepository<Subject> {
  static final SubjectsRepository _instance = SubjectsRepository._internal();
  static SubjectsRepository get instance => _instance;
  SubjectsRepository._internal();

  @override
  String get collectionName => 'subjects';

  @override
  String get storageKey => 'subjects';

  @override
  Subject fromFirestore(Map<String, dynamic> data, String documentId) {
    return Subject.fromFirestore(data, documentId);
  }

  @override
  Map<String, dynamic> toLocalMap(Subject item) {
    return item.toLocalMap();
  }

  @override
  Subject fromLocalMap(Map<String, dynamic> map) {
    return Subject.fromLocalMap(map);
  }

  @override
  Map<String, dynamic> toFirestore(Subject item) {
    return item.toFirestore();
  }

  @override
  Future<List<Subject>> loadFromLocalStorage() async {
    try {
      final file = File(
        '${persistentStorage.storageDirectory.path}/subjects.json',
      );
      if (!await file.exists()) {
        // إذا لم توجد بيانات محلية، استخدم البيانات الافتراضية
        final defaultSubjects = SimpleDataService.instance.getSubjects();
        debugPrint(
          '📱 تم تحميل ${defaultSubjects.length} مادة من البيانات الافتراضية',
        );
        return defaultSubjects;
      }

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final subjects = data.map((item) => fromLocalMap(item)).toList();

      debugPrint('📱 تم تحميل ${subjects.length} مادة من التخزين المحلي');
      return subjects;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المواد من التخزين المحلي: $e');
      // في حالة الخطأ، إرجاع البيانات الافتراضية
      return SimpleDataService.instance.getSubjects();
    }
  }

  @override
  Future<void> saveToLocalStorage(List<Subject> data) async {
    try {
      final file = File(
        '${persistentStorage.storageDirectory.path}/subjects.json',
      );
      final jsonData = data.map((subject) => toLocalMap(subject)).toList();
      await file.writeAsString(jsonEncode(jsonData));

      debugPrint('✅ تم حفظ ${data.length} مادة في التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ المواد في التخزين المحلي: $e');
    }
  }

  /// الحصول على المواد النشطة فقط
  Future<List<Subject>> getActiveSubjects() async {
    final allSubjects = await getLocalData();
    return allSubjects.where((subject) => subject.isActive).toList();
  }

  /// الحصول على المواد المدفوعة النشطة
  Future<List<Subject>> getPaidActiveSubjects() async {
    final allSubjects = await getLocalData();
    return allSubjects
        .where((subject) => subject.isActive && !subject.isFree)
        .toList();
  }

  /// الحصول على المواد المجانية النشطة
  Future<List<Subject>> getFreeActiveSubjects() async {
    final allSubjects = await getLocalData();
    return allSubjects
        .where((subject) => subject.isActive && subject.isFree)
        .toList();
  }

  /// الحصول على المواد لقسم معين
  Future<List<Subject>> getSubjectsBySection(String sectionId) async {
    final allSubjects = await getLocalData();
    return allSubjects
        .where((subject) => subject.sectionId == sectionId && subject.isActive)
        .toList();
  }

  /// الحصول على المواد المدفوعة لقسم معين
  Future<List<Subject>> getPaidSubjectsBySection(String sectionId) async {
    final allSubjects = await getLocalData();
    return allSubjects
        .where(
          (subject) =>
              subject.sectionId == sectionId &&
              subject.isActive &&
              !subject.isFree,
        )
        .toList();
  }

  /// البحث في المواد
  Future<List<Subject>> searchSubjects(String query) async {
    final allSubjects = await getLocalData();
    final lowerQuery = query.toLowerCase();

    return allSubjects
        .where(
          (subject) =>
              subject.name.toLowerCase().contains(lowerQuery) ||
              subject.description.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  /// الحصول على مادة بواسطة المعرف
  Future<Subject?> getSubjectById(String id) async {
    final allSubjects = await getLocalData();
    try {
      return allSubjects.firstWhere((subject) => subject.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تهيئة البيانات الأولية
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة repository المواد...');

      // تحميل البيانات المحلية أولاً
      final localSubjects = await getLocalData();
      debugPrint('📱 تم تحميل ${localSubjects.length} مادة محلياً');

      // التحقق من التحديثات في الخلفية
      _checkForUpdatesInBackground();

      debugPrint('✅ تم تهيئة repository المواد بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة repository المواد: $e');
    }
  }

  /// التحقق من التحديثات في الخلفية
  void _checkForUpdatesInBackground() {
    Future.delayed(Duration.zero, () async {
      try {
        if (await hasRemoteUpdates()) {
          debugPrint('🔄 يوجد تحديثات جديدة للمواد، بدء المزامنة...');
          await fetchAndCacheRemoteData();
        }
      } catch (e) {
        debugPrint('❌ خطأ في التحقق من تحديثات المواد: $e');
      }
    });
  }

  /// مزامنة دورية (يمكن استدعاؤها من خدمة المزامنة في الخلفية)
  Future<void> periodicSync() async {
    try {
      debugPrint('🔄 مزامنة دورية للمواد...');
      await syncIfNeeded();
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الدورية للمواد: $e');
    }
  }

  /// الحصول على إحصائيات المواد
  Future<Map<String, int>> getSubjectsStats() async {
    final allSubjects = await getLocalData();

    return {
      'total': allSubjects.length,
      'active': allSubjects.where((s) => s.isActive).length,
      'inactive': allSubjects.where((s) => !s.isActive).length,
      'free': allSubjects.where((s) => s.isFree).length,
      'paid': allSubjects.where((s) => !s.isFree).length,
    };
  }

  /// الحصول على المواد مجمعة حسب القسم
  Future<Map<String, List<Subject>>> getSubjectsGroupedBySection() async {
    final allSubjects = await getActiveSubjects();
    final Map<String, List<Subject>> grouped = {};

    for (final subject in allSubjects) {
      if (!grouped.containsKey(subject.sectionId)) {
        grouped[subject.sectionId] = [];
      }
      grouped[subject.sectionId]!.add(subject);
    }

    return grouped;
  }
}
