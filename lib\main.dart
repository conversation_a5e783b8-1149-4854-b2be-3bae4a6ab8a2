import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import 'flavors.dart';
import 'app.dart';
import 'firebase_options_student.dart';
import 'firebase_options_admin.dart' as admin_options;
import 'shared/services/encryption_service.dart';
import 'shared/services/video_service.dart';
import 'shared/services/section_service.dart';
import 'shared/services/content_service.dart';
import 'shared/services/local_questions_cache_service.dart';
// النظام الجديد Offline-First
import 'shared/services/unified_offline_service.dart';
import 'shared/services/fcm_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

/// معالج رسائل FCM في الخلفية
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('📨 تم استقبال رسالة FCM في الخلفية: ${message.messageId}');
}

// 🔒 دالة آمنة للطباعة - تعمل فقط في Debug mode
void safePrint(Object? message) {
  if (kDebugMode) {
    print(message);
  }
}

// 🛡️ حماية بسيطة ضد أدوات التحليل
void _initAntiDebugging() {
  if (kReleaseMode) {
    // تشويش بسيط للذاكرة
    final dummy = List.generate(100, (i) => 'dummy_data_$i');
    dummy.clear(); // تنظيف فوري

    // منع بعض أدوات التحليل البسيطة
    try {
      if (Platform.isWindows) {
        // فحص بسيط لعمليات مشبوهة
        _checkSuspiciousProcesses();
      }
    } catch (e) {
      // تجاهل الأخطاء في الحماية
    }
  }
}

// فحص العمليات المشبوهة (بسيط جداً)
void _checkSuspiciousProcesses() {
  // قائمة بسيطة من أدوات التحليل الشائعة
  final suspiciousProcesses = [
    'wireshark',
    'fiddler',
    'procmon',
    'processhacker',
    'x64dbg',
    'ollydbg',
    'ida',
    'cheatengine',
  ];

  // هذا مجرد تحذير بسيط - لا يوقف التطبيق
  for (final process in suspiciousProcesses) {
    // في التطبيق الحقيقي، يمكن فحص العمليات النشطة
    // لكن هنا نكتفي بالتحذير البسيط
  }
}

void main() async {
  // 🔒 إعدادات الأمان - منع عرض الروابط في Release
  if (kReleaseMode) {
    // في وضع Release: إيقاف debugPrint
    debugPrint = (String? message, {int? wrapWidth}) {};

    // 🛡️ حماية إضافية ضد أدوات التحليل البسيطة
    _initAntiDebugging();
  }

  // معالجة الأخطاء العامة
  FlutterError.onError = (FlutterErrorDetails details) {
    // فقط في Debug mode
    if (kDebugMode) {
      safePrint('🔴 خطأ Flutter: ${details.exception}');
      safePrint('🔍 التفاصيل: ${details.stack}');
    }
  };

  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة FCM background handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // video_player_win يعمل تلقائياً مع video_player العادي
  if (Platform.isWindows) {
    debugPrint('✅ video_player_win جاهز للاستخدام على Windows');
  } else {
    debugPrint('✅ video_player جاهز للاستخدام على Android/iOS');
  }

  try {
    // تحديد النكهة بناءً على متغير البيئة
    const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'student');
    safePrint('🚀 تشغيل التطبيق بنكهة: $flavor');

    if (flavor == 'admin') {
      F.appFlavor = Flavor.admin;
      await Firebase.initializeApp(
        options: admin_options.DefaultFirebaseOptions.currentPlatform,
      );
    } else {
      F.appFlavor = Flavor.student;
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // تهيئة خدمة التشفير
    EncryptionService.instance.initialize();

    // تهيئة خدمة الفيديو
    await VideoService.instance.initialize();

    // تهيئة خدمة الأقسام
    await SectionService.instance.initialize();

    // تهيئة خدمة المحتوى
    await ContentService.instance.initialize();

    // تنظيف البيانات التالفة
    await LocalQuestionsCacheService.instance.cleanupOnStartup();

    // 🚀 تهيئة النظام الجديد Offline-First
    safePrint('🚀 بدء تهيئة النظام الجديد Offline-First...');
    try {
      await UnifiedOfflineService.instance.initialize();
      safePrint('✅ تم تهيئة النظام الجديد بنجاح');
    } catch (e) {
      safePrint('⚠️ خطأ في تهيئة النظام الجديد: $e');
      safePrint('🔄 سيتم المتابعة بالنظام القديم');
    }

    runApp(const MyApp());
  } catch (e, stackTrace) {
    safePrint('🔴 خطأ في تهيئة التطبيق: $e');
    safePrint('🔍 Stack trace: $stackTrace');

    // تشغيل التطبيق حتى لو فشلت بعض الخدمات
    runApp(const MyApp());
  }
}
