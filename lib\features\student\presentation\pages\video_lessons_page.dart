import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_lesson_model.dart';
import '../../../../shared/services/video_service.dart';
import '../../../../shared/services/subscription_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/unified_offline_service.dart';
import 'video_list_page.dart';

/// صفحة دروس الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoLessonsPage extends StatefulWidget {
  final String unitId;
  final bool hasSubscription;

  const VideoLessonsPage({
    super.key,
    required this.unitId,
    required this.hasSubscription,
  });

  @override
  State<VideoLessonsPage> createState() => _VideoLessonsPageState();
}

class _VideoLessonsPageState extends State<VideoLessonsPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  final VideoService _videoService = VideoService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;

  List<VideoLesson> _lessons = [];
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadVideoLessonsImmediately();
  }

  /// تحميل فوري لدروس الفيديو من النظام الجديد
  Future<void> _loadVideoLessonsImmediately() async {
    try {
      // استخدام النظام القديم مؤقتاً حتى يتم تطبيق النظام الجديد بالكامل
      await _loadDataImmediately();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل دروس الفيديو: $e');
    }
  }

  /// تحميل البيانات فوراً بدون انتظار
  void _loadDataImmediately() {
    // محاولة تحميل البيانات المحفوظة أولاً
    _loadSavedDataFirst();
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // فحص البيانات المحلية من الكاش أولاً (بدون await)
      final cachedLessons = _videoService.getCachedVideoLessons(widget.unitId);
      final activeCachedLessons = cachedLessons
          .where((lesson) => lesson.isActive)
          .toList();

      if (activeCachedLessons.isNotEmpty) {
        // البيانات متوفرة في الكاش - عرضها فوراً
        setState(() {
          _lessons = activeCachedLessons;
        });
        debugPrint(
          '⚡ إرجاع ${activeCachedLessons.length} درس فيديو من الكاش للوحدة ${widget.unitId}',
        );
        debugPrint(
          '🚀 تم عرض ${activeCachedLessons.length} درس فيديو من البيانات المحفوظة',
        );

        // تحميل بيانات الاشتراك في الخلفية
        _loadSubscriptionInBackground();

        // بدء التحديث في الخلفية
        _updateFromNetworkSilently();
        return;
      }

      // إذا لم توجد بيانات في الكاش، حاول تحميل البيانات من Firebase
      debugPrint('📱 لا توجد دروس في الكاش، محاولة تحميل من Firebase...');

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();

      // محاولة تحميل البيانات من Firebase
      try {
        final savedLessons = await _videoService.getVideoLessons(widget.unitId);
        final activeSavedLessons = savedLessons
            .where((lesson) => lesson.isActive)
            .toList();

        setState(() {
          _lessons = activeSavedLessons;
        });

        if (activeSavedLessons.isNotEmpty) {
          debugPrint(
            '✅ تم تحميل ${activeSavedLessons.length} درس فيديو من Firebase',
          );
        } else {
          debugPrint('📱 لا توجد دروس نشطة للوحدة ${widget.unitId}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات من Firebase: $e');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');

      // تحميل بيانات الاشتراك في الخلفية
      _loadSubscriptionInBackground();
    }
  }

  /// تحميل بيانات الاشتراك في الخلفية
  Future<void> _loadSubscriptionInBackground() async {
    try {
      await _subscriptionService.loadUserSubscription();
      // تحديث صامت من الشبكة في الخلفية
      _updateFromNetworkSilently();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
    }
  }

  /// تحديث صامت من الشبكة في الخلفية
  Future<void> _updateFromNetworkSilently() async {
    try {
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        debugPrint('📱 لا توجد شبكة - الاعتماد على البيانات المحلية فقط');
        return;
      }

      debugPrint('🌐 تحديث دروس الفيديوهات من الشبكة في الخلفية...');
      // تحديث الدروس من Firebase باستخدام الدالة الجديدة
      final updatedLessons = await _videoService
          .refreshVideoLessonsFromFirebase(widget.unitId);

      if (mounted && updatedLessons.isNotEmpty) {
        final activeUpdatedLessons = updatedLessons
            .where((lesson) => lesson.isActive)
            .toList();
        setState(() {
          _lessons = activeUpdatedLessons;
        });
        debugPrint(
          '🔄 تم تحديث ${activeUpdatedLessons.length} درس فيديو من الشبكة',
        );
      }
    } catch (e) {
      debugPrint(
        '⚠️ فشل التحديث من الشبكة (لا مشكلة - البيانات المحلية متوفرة): $e',
      );
    }
  }

  /// فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// تحديث يدوي (Pull-to-Refresh)
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي لدروس الفيديوهات...');
      final lessons = await _videoService.refreshVideoLessonsFromFirebase(
        widget.unitId,
      );
      final activeLessons = lessons.where((lesson) => lesson.isActive).toList();

      if (mounted) {
        setState(() {
          _lessons = activeLessons;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${activeLessons.length} درس فيديو يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'الدروس',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshData,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة مع إمكانية التحديث
    return RefreshIndicator(
      onRefresh: _refreshVideoLessons,
      child: _lessons.isNotEmpty ? _buildLessonsContent() : _buildEmptyState(),
    );
  }

  /// تحديث يدوي مع مزامنة (النظام الجديد)
  Future<void> _refreshVideoLessons() async {
    if (_isRefreshing) return;

    setState(() => _isRefreshing = true);

    try {
      await _offlineService.manualSync();
      await _loadVideoLessonsImmediately();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث دروس الفيديو: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildLessonsContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildLessonsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.play_lesson, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'دروس الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر الدرس لمشاهدة الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLessonsList() {
    return ListView.builder(
      itemCount: _lessons.length,
      itemBuilder: (context, index) {
        final lesson = _lessons[index];
        return _buildLessonCard(lesson);
      },
    );
  }

  Widget _buildLessonCard(VideoLesson lesson) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToVideos(lesson),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildLessonIcon(lesson),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        lesson.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      if (lesson.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          lesson.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textSecondaryColor,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLessonIcon(VideoLesson lesson) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(Icons.play_lesson, color: Colors.white, size: 24.sp),
    );
  }

  void _navigateToVideos(VideoLesson lesson) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoListPage(
          lessonId: lesson.id,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_lesson_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد دروس فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الدروس قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
