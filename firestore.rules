rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ==================== قواعد الأمان الدائمة ====================
    // PERMANENT SECURITY RULES FOR PRODUCTION

    // دالة مساعدة للتحقق من دور المستخدم
    function isAdmin() {
      return request.auth != null && (
        // السماح للمستخدمين المجهولين بصلاحيات الأدمن (للتطبيق الإداري)
        request.auth.token.firebase.sign_in_provider == 'anonymous' ||
        // أو التحقق من دور المستخدم في قاعدة البيانات
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin')
      );
    }

    function isAuthenticated() {
      return request.auth != null;
    }

    // قواعد المستخدمين - كل مستخدم يمكنه الوصول لبياناته فقط
    match /users/{userId} {
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
    }

    // قواعد الأقسام - قراءة للجميع، كتابة للإدارة فقط
    match /sections/{sectionId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد المواد - قراءة للجميع، كتابة للإدارة فقط
    match /subjects/{subjectId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد أقسام الفيديو - قراءة للجميع، كتابة للإدارة فقط
    match /video_sections/{sectionId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد مواد الفيديو - قراءة للجميع، كتابة للإدارة فقط
    match /video_subjects/{subjectId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد المحتوى التعليمي - قراءة للجميع، كتابة للإدارة فقط
    match /units/{unitId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /lessons/{lessonId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /questions/{questionId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /video_units/{unitId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /video_lessons/{lessonId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /videos/{videoId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد بيانات المستخدمين - كل مستخدم يمكنه الوصول لبياناته فقط
    match /user_statistics/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    match /user_favorites/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    match /user_wrong_answers/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    match /user_subscriptions/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    match /user_notes/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    match /user_video_progress/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    // قواعد البيانات الموحدة للمستخدمين - قراءة وكتابة للجميع المصادق عليهم
    match /user_data/{deviceId} {
      allow read, write: if isAuthenticated();
    }

    // قواعد البيانات الموحدة العامة - قراءة للجميع، كتابة للإدارة فقط
    match /unified_data/{documentId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // قواعد الأكواد - قراءة للجميع، كتابة للإدارة والطلاب (لتحديث حالة الكود)
    match /subscription_codes/{codeId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated(); // السماح للطلاب بتحديث حالة الكود عند الاستخدام
    }

    match /activation_codes/{codeId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // قواعد الإعدادات العامة - قراءة للجميع، كتابة للإدارة فقط
    match /settings/{settingId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /pricing_messages/{messageId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /admin_messages/{messageId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /announcements/{announcementId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /app_config/{configId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /version_info/{versionId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد للمجموعات الفرعية
    match /{path=**}/questions/{questionId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /{path=**}/videos/{videoId} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // قواعد خاصة لمجموعات metadata
    match /sections_metadata/{document} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /subjects_metadata/{document} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /video_sections_metadata/{document} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    match /video_subjects_metadata/{document} {
      allow read: if true; // السماح بالقراءة للجميع
      allow write: if isAdmin();
    }

    // منع الوصول لأي مجموعات أخرى غير محددة
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
