rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ==================== قواعد مؤقتة للاختبار ====================
    // TEMPORARY RULES FOR TESTING - REMOVE IN PRODUCTION

    // قواعد للمجموعات العامة - مؤقتة بدون مصادقة
    // Public collections rules - temporary without authentication
    match /subjects/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /sections/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /exams/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /units/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /lessons/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /questions/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /courses/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /videos/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد للإعدادات العامة
    // Public settings
    match /settings/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /pricing_messages/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد لأقسام الفيديوهات
    // Video sections rules
    match /video_sections/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد لأقسام الاختبارات
    // Test sections rules
    match /test_sections/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد للرسائل الإدارية
    // Admin messages rules
    match /admin_messages/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد للإعلانات
    // Announcements rules
    match /announcements/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد لبيانات المستخدمين - مؤقتة بدون مصادقة
    // User data rules - temporary without authentication
    match /user_statistics/{deviceId} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /user_favorites/{deviceId} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /user_wrong_answers/{deviceId} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /user_subscriptions/{deviceId} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /user_notes/{deviceId} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /user_video_progress/{deviceId} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد للأكواد - مؤقتة بدون مصادقة
    // Codes rules - temporary without authentication
    match /subscription_codes/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /activation_codes/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد للمجموعات الفرعية في الوثائق
    // Rules for subcollections
    match /{path=**}/questions/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /{path=**}/videos/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قواعد عامة للمجموعات الأخرى
    // General rules for other collections
    match /app_config/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    match /version_info/{document} {
      allow read, write: if true; // مؤقت للاختبار
    }

    // قاعدة عامة للمجموعات الأخرى - مؤقتة للاختبار
    // General rule for other collections - temporary for testing
    match /{document=**} {
      allow read, write: if true; // مؤقت للاختبار - يجب تغييرها في الإنتاج
    }
  }
}
