import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/video_section_model.dart';
import '../services/simple_data_service.dart';
import 'base_repository.dart';

/// Repository لأقسام الفيديوهات مع دعم Offline-First
class VideoSectionsRepository extends BaseRepository<VideoSection> {
  static final VideoSectionsRepository _instance =
      VideoSectionsRepository._internal();
  static VideoSectionsRepository get instance => _instance;
  VideoSectionsRepository._internal();

  @override
  String get collectionName => 'video_sections';

  @override
  String get storageKey => 'video_sections';

  @override
  VideoSection fromFirestore(Map<String, dynamic> data, String documentId) {
    return VideoSection.fromFirestore(data, documentId);
  }

  @override
  Map<String, dynamic> toLocalMap(VideoSection item) {
    return item.toLocalMap();
  }

  @override
  VideoSection fromLocalMap(Map<String, dynamic> map) {
    return VideoSection.fromLocalMap(map);
  }

  @override
  Map<String, dynamic> toFirestore(VideoSection item) {
    return item.toFirestore();
  }

  @override
  Future<List<VideoSection>> loadFromLocalStorage() async {
    try {
      final file = File(
        '${persistentStorage.storageDirectory.path}/video_sections.json',
      );
      if (!await file.exists()) {
        // إذا لم توجد بيانات محلية، استخدم البيانات الافتراضية
        final defaultSections = SimpleDataService.instance.getVideoSections();
        debugPrint(
          '📱 تم تحميل ${defaultSections.length} قسم فيديو من البيانات الافتراضية',
        );
        return defaultSections;
      }

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      final sections = data.map((item) => fromLocalMap(item)).toList();

      debugPrint('📱 تم تحميل ${sections.length} قسم فيديو من التخزين المحلي');
      return sections;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل أقسام الفيديوهات من التخزين المحلي: $e');
      // في حالة الخطأ، إرجاع البيانات الافتراضية
      return SimpleDataService.instance.getVideoSections();
    }
  }

  @override
  Future<void> saveToLocalStorage(List<VideoSection> data) async {
    try {
      final file = File(
        '${persistentStorage.storageDirectory.path}/video_sections.json',
      );
      final jsonData = data.map((section) => toLocalMap(section)).toList();
      await file.writeAsString(jsonEncode(jsonData));

      debugPrint('✅ تم حفظ ${data.length} قسم فيديو في التخزين المحلي');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ أقسام الفيديوهات في التخزين المحلي: $e');
    }
  }

  /// الحصول على أقسام الفيديوهات النشطة فقط
  Future<List<VideoSection>> getActiveSections() async {
    final allSections = await getLocalData();

    debugPrint('🔍 جميع أقسام الفيديو المحفوظة:');
    for (var section in allSections) {
      debugPrint('   📂 قسم: ${section.name}');
      debugPrint('      - id: "${section.id}"');
      debugPrint('      - isActive: ${section.isActive}');
    }

    final activeSections = allSections
        .where((section) => section.isActive)
        .toList();
    debugPrint('🔍 الأقسام النشطة: ${activeSections.length}');

    return activeSections;
  }

  /// البحث في أقسام الفيديوهات
  Future<List<VideoSection>> searchSections(String query) async {
    final allSections = await getLocalData();
    final lowerQuery = query.toLowerCase();

    return allSections
        .where(
          (section) =>
              section.name.toLowerCase().contains(lowerQuery) ||
              section.description.toLowerCase().contains(lowerQuery),
        )
        .toList();
  }

  /// الحصول على قسم فيديو بواسطة المعرف
  Future<VideoSection?> getSectionById(String id) async {
    final allSections = await getLocalData();
    try {
      return allSections.firstWhere((section) => section.id == id);
    } catch (e) {
      return null;
    }
  }

  /// تهيئة البيانات الأولية
  Future<void> initialize() async {
    try {
      debugPrint('🔄 تهيئة repository أقسام الفيديوهات...');

      // تحميل البيانات المحلية أولاً
      final localSections = await getLocalData();
      debugPrint('📱 تم تحميل ${localSections.length} قسم فيديو محلياً');

      // التحقق من التحديثات في الخلفية
      _checkForUpdatesInBackground();

      debugPrint('✅ تم تهيئة repository أقسام الفيديوهات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة repository أقسام الفيديوهات: $e');
    }
  }

  /// التحقق من التحديثات في الخلفية
  void _checkForUpdatesInBackground() {
    Future.delayed(Duration.zero, () async {
      try {
        if (await hasRemoteUpdates()) {
          debugPrint(
            '🔄 يوجد تحديثات جديدة لأقسام الفيديوهات، بدء المزامنة...',
          );
          await fetchAndCacheRemoteData();
        }
      } catch (e) {
        debugPrint('❌ خطأ في التحقق من تحديثات أقسام الفيديوهات: $e');
      }
    });
  }

  /// مزامنة دورية (يمكن استدعاؤها من خدمة المزامنة في الخلفية)
  Future<void> periodicSync() async {
    try {
      debugPrint('🔄 مزامنة دورية لأقسام الفيديوهات...');
      await syncIfNeeded();
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الدورية لأقسام الفيديوهات: $e');
    }
  }

  /// الحصول على إحصائيات أقسام الفيديوهات
  Future<Map<String, int>> getSectionsStats() async {
    final allSections = await getLocalData();

    return {
      'total': allSections.length,
      'active': allSections.where((s) => s.isActive).length,
      'inactive': allSections.where((s) => !s.isActive).length,
    };
  }

  /// الحصول على أقسام الفيديو النشطة (فوري من الذاكرة)
  Future<List<VideoSection>> getActiveVideoSections() async {
    final allSections = await getLocalData();
    return allSections.where((section) => section.isActive).toList();
  }

  /// البحث في أقسام الفيديو (فوري من الذاكرة)
  Future<List<VideoSection>> searchVideoSections(String query) async {
    final allSections = await getLocalData();
    final searchQuery = query.toLowerCase().trim();

    if (searchQuery.isEmpty) {
      return getActiveVideoSections();
    }

    return allSections.where((section) {
      return section.isActive &&
          (section.name.toLowerCase().contains(searchQuery) ||
              section.description.toLowerCase().contains(searchQuery));
    }).toList();
  }
}
