import 'package:flutter/foundation.dart';

class Subject {
  final String id;
  final String name;
  final String description;
  final String iconUrl;
  final String color;
  final bool isActive;
  final bool isFree; // true = مجانية (تجريبية), false = مدفوعة
  final String sectionId; // معرف القسم الذي تنتمي إليه المادة
  final DateTime createdAt;
  final DateTime updatedAt;

  Subject({
    required this.id,
    required this.name,
    required this.description,
    this.iconUrl = '',
    this.color = '#6C5CE7',
    this.isActive = true,
    this.isFree = false, // افتراضياً مدفوعة
    this.sectionId = '', // افتراضياً فارغ للمواد القديمة
    required this.createdAt,
    required this.updatedAt,
  });

  factory Subject.fromMap(Map<String, dynamic> map) {
    return Subject(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      isActive: map['isActive'] ?? true,
      isFree: map['isFree'] ?? false,
      sectionId: map['sectionId'] ?? '',
      createdAt: _parseDateTime(map['createdAt']),
      updatedAt: _parseDateTime(map['updatedAt']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'isActive': isActive,
      'isFree': isFree,
      'sectionId': sectionId,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
    };
  }

  Subject copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    String? color,
    bool? isActive,
    bool? isFree,
    String? sectionId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subject(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      isFree: isFree ?? this.isFree,
      sectionId: sectionId ?? this.sectionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // دوال Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'isActive': isActive,
      'isFree': isFree, // تأكد من حفظ القيمة حتى لو كانت false
      'sectionId': sectionId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  factory Subject.fromFirestore(Map<String, dynamic> data, String documentId) {
    return Subject(
      id: documentId,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      iconUrl: data['iconUrl'] ?? '',
      color: data['color'] ?? '#6C5CE7',
      isActive: data['isActive'] ?? true,
      isFree: data['isFree'] ?? false,
      sectionId: data['sectionId'] ?? '',
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    try {
      // إذا كان DateTime بالفعل
      if (value is DateTime) {
        return value;
      }

      // إذا كان int (milliseconds)
      if (value is int) {
        return DateTime.fromMillisecondsSinceEpoch(value);
      }

      // إذا كان Timestamp من Firestore
      if (value.runtimeType.toString().contains('Timestamp')) {
        return value.toDate();
      }

      // إذا كان String
      if (value is String) {
        return DateTime.tryParse(value) ?? DateTime.now();
      }

      // إذا كان Map (من Firestore أحياناً)
      if (value is Map) {
        if (value.containsKey('_seconds')) {
          return DateTime.fromMillisecondsSinceEpoch(value['_seconds'] * 1000);
        }
      }
    } catch (e) {
      debugPrint(
        'خطأ في تحويل التاريخ: $e, القيمة: $value, النوع: ${value.runtimeType}',
      );
    }

    return DateTime.now();
  }

  /// تحويل إلى Map للتخزين المحلي
  Map<String, dynamic> toLocalMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconUrl': iconUrl,
      'color': color,
      'isActive': isActive,
      'isFree': isFree,
      'sectionId': sectionId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء من Map للتخزين المحلي
  factory Subject.fromLocalMap(Map<String, dynamic> map) {
    return Subject(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      iconUrl: map['iconUrl'] ?? '',
      color: map['color'] ?? '#6C5CE7',
      isActive: map['isActive'] ?? true,
      isFree: map['isFree'] ?? false,
      sectionId: map['sectionId'] ?? '',
      createdAt: DateTime.tryParse(map['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(map['updatedAt'] ?? '') ?? DateTime.now(),
    );
  }
}
