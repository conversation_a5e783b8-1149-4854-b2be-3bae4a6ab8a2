rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // ==================== قواعد النظام الجديد Offline-First ====================
    
    // البيانات الوصفية للتحديثات - قراءة للجميع، كتابة للإدارة فقط
    match /sections_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /subjects_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /subject_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /video_sections_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /video_subjects_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /subject_videos_metadata/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // ==================== القواعد الحالية ====================
    
    // الأقسام - قراءة للجميع، كتابة للإدارة فقط
    match /sections/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // المواد - قراءة للجميع، كتابة للإدارة فقط
    match /subjects/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // الوحدات - قراءة للجميع، كتابة للإدارة فقط
    match /units/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // الدروس - قراءة للجميع، كتابة للإدارة فقط
    match /lessons/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // الأسئلة - قراءة للجميع، كتابة للإدارة فقط
    match /questions/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // أقسام الفيديوهات - قراءة للجميع، كتابة للإدارة فقط
    match /video_sections/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // مواد الفيديوهات - قراءة للجميع، كتابة للإدارة فقط
    match /video_subjects/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // وحدات الفيديوهات - قراءة للجميع، كتابة للإدارة فقط
    match /video_units/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // دروس الفيديوهات - قراءة للجميع، كتابة للإدارة فقط
    match /video_lessons/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // الفيديوهات - قراءة للجميع، كتابة للإدارة فقط
    match /videos/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // اشتراكات المستخدمين - قراءة وكتابة للمستخدم نفسه أو الإدارة
    match /user_subscriptions/{userId} {
      allow read, write: if request.auth != null && 
                         (request.auth.uid == userId || isAdmin());
    }
    
    // أكواد الاشتراك - قراءة للجميع، كتابة للإدارة فقط
    match /subscription_codes/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // معلومات التسعير - قراءة للجميع، كتابة للإدارة فقط
    match /pricing_info/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // معلومات الاتصال - قراءة للجميع، كتابة للإدارة فقط
    match /contact_info/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // الإعدادات العامة - قراءة للجميع، كتابة للإدارة فقط
    match /app_settings/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // الإشعارات - قراءة للجميع، كتابة للإدارة فقط
    match /notifications/{document} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // سجل الأنشطة - كتابة للجميع، قراءة للإدارة فقط
    match /activity_logs/{document} {
      allow create: if request.auth != null;
      allow read: if isAdmin();
    }
    
    // الإحصائيات - قراءة وكتابة للإدارة فقط
    match /statistics/{document} {
      allow read, write: if isAdmin();
    }
    
    // ==================== دوال مساعدة ====================
    
    // دالة للتحقق من صلاحيات الإدارة
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // دالة للتحقق من أن المستخدم مسجل دخول
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // دالة للتحقق من ملكية المورد
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // دالة للتحقق من صحة البيانات
    function isValidData(requiredFields) {
      return request.resource.data.keys().hasAll(requiredFields);
    }
    
    // دالة للتحقق من أن البيانات لم تتغير
    function unchangedFields(fields) {
      return fields.all(field, 
        resource.data[field] == request.resource.data[field]
      );
    }
  }
}

// ==================== ملاحظات مهمة ====================

/*
هذه القواعد مصممة للنظام الجديد Offline-First وتحقق الأهداف التالية:

1. تقليل عدد القراءات:
   - البيانات الوصفية للتحديثات متاحة للقراءة للجميع
   - التطبيق يقرأ البيانات الوصفية فقط للتحقق من التحديثات
   - البيانات الفعلية تُحمل فقط عند الحاجة

2. الأمان:
   - جميع عمليات الكتابة محصورة بالإدارة
   - المستخدمون يمكنهم قراءة البيانات العامة فقط
   - اشتراكات المستخدمين محمية

3. المرونة:
   - دوال مساعدة لسهولة الصيانة
   - قواعد واضحة ومنظمة
   - دعم للميزات المستقبلية

4. الأداء:
   - قواعد محسنة لتقليل وقت التحقق
   - تجنب الاستعلامات المعقدة
   - دعم للتخزين المؤقت

لتطبيق هذه القواعد:
1. انسخ المحتوى إلى ملف firestore.rules
2. ارفعه إلى Firebase Console
3. اختبر القواعد باستخدام Firebase Rules Playground
4. راقب الأداء في Firebase Console
*/
