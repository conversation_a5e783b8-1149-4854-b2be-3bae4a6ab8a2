// ملف وهمي مؤقت - يحتوي على جميع الدوال المطلوبة للتوافق
import 'package:flutter/foundation.dart';
import '../../core/models/section.dart';
import '../models/subject_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_model.dart';

class UnifiedOfflineService extends ChangeNotifier {
  static UnifiedOfflineService get instance => UnifiedOfflineService();

  int get unreadNotificationsCount => 0;
  List<dynamic> get notifications => [];
  String get systemStatus => 'نشط';

  Future<void> initialize() async {}
  bool hasActiveSubscription() => false;
  Future<void> manualSync() async {}
  Future<List<dynamic>> searchSections(String query) async => [];

  // دوال الحصول على البيانات
  Future<List<Section>> getActiveSections() async => [];
  Future<List<Subject>> getActiveSubjectsBySection(String sectionId) async =>
      [];
  Future<List<VideoSection>> getActiveVideoSections() async => [];
  Future<List<VideoSubject>> getActiveVideoSubjectsBySection(
    String sectionId,
  ) async => [];
  Future<List<Video>> getActiveVideosByLesson(String lessonId) async => [];
}
