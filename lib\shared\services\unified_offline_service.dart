import 'package:flutter/foundation.dart';
import 'offline_first_service.dart';
import 'local_notifications_service.dart';
import 'subscription_service.dart';
import 'background_sync_service.dart';
import 'fcm_service.dart';

/// خدمة موحدة تجمع جميع خدمات النظام الجديد Offline-First
/// هذه هي النقطة الوحيدة للوصول لجميع البيانات في التطبيق
class UnifiedOfflineService extends ChangeNotifier {
  static final UnifiedOfflineService _instance =
      UnifiedOfflineService._internal();
  static UnifiedOfflineService get instance => _instance;
  UnifiedOfflineService._internal();

  final OfflineFirstService _offlineFirstService = OfflineFirstService.instance;
  final LocalNotificationsService _notificationsService =
      LocalNotificationsService.instance;
  final SubscriptionService _subscriptionService = SubscriptionService.instance;
  final BackgroundSyncService _backgroundSyncService =
      BackgroundSyncService.instance;
  final FCMService _fcmService = FCMService.instance;

  bool _isInitialized = false;

  /// تهيئة النظام الكامل
  Future<void> initialize() async {
    try {
      debugPrint('🚀 تهيئة النظام الموحد Offline-First...');

      // تهيئة خدمة الإشعارات أولاً
      await _notificationsService.initialize();

      // تهيئة خدمة الاشتراكات
      await _subscriptionService.initialize();

      // تهيئة خدمة FCM
      await _fcmService.initialize();

      // تهيئة خدمة المزامنة في الخلفية
      await _backgroundSyncService.initialize();

      // تهيئة النظام الأساسي
      await _offlineFirstService.initialize();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة النظام الموحد بنجاح');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة النظام الموحد: $e');
    }
  }

  /// التحقق من حالة التهيئة
  bool get isInitialized =>
      _isInitialized && _offlineFirstService.isInitialized;

  // ==================== الأقسام ====================

  /// الحصول على الأقسام النشطة (فوري من الذاكرة)
  Future<List<dynamic>> getActiveSections() async {
    return await _offlineFirstService.getActiveSections();
  }

  /// البحث في الأقسام (فوري من الذاكرة)
  Future<List<dynamic>> searchSections(String query) async {
    return await _offlineFirstService.searchSections(query);
  }

  // ==================== أقسام الفيديو ====================

  /// الحصول على أقسام الفيديو النشطة (فوري من الذاكرة)
  Future<List<dynamic>> getActiveVideoSections() async {
    return await _offlineFirstService.getActiveVideoSections();
  }

  /// البحث في أقسام الفيديو (فوري من الذاكرة)
  Future<List<dynamic>> searchVideoSections(String query) async {
    return await _offlineFirstService.searchVideoSections(query);
  }

  /// الحصول على مواد الفيديو النشطة لقسم معين (فوري من الذاكرة)
  Future<List<dynamic>> getActiveVideoSubjectsBySection(
    String sectionId,
  ) async {
    return await _offlineFirstService.getActiveVideoSubjectsBySection(
      sectionId,
    );
  }

  // ==================== المواد ====================

  /// الحصول على المواد النشطة لقسم معين (فوري من الذاكرة)
  Future<List<dynamic>> getActiveSubjectsBySection(String sectionId) async {
    return await _offlineFirstService.getActiveSubjectsBySection(sectionId);
  }

  /// الحصول على المواد المدفوعة لقسم معين (فوري من الذاكرة)
  Future<List<dynamic>> getPaidSubjectsBySection(String sectionId) async {
    return await _offlineFirstService.getPaidSubjectsBySection(sectionId);
  }

  /// البحث في المواد (فوري من الذاكرة)
  Future<List<dynamic>> searchSubjects(String query) async {
    return await _offlineFirstService.searchSubjects(query);
  }

  // ==================== محتوى المواد ====================

  /// الحصول على وحدات مادة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getSubjectUnits(String subjectId) async {
    return await _offlineFirstService.getSubjectUnits(subjectId);
  }

  /// الحصول على دروس مادة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getSubjectLessons(String subjectId) async {
    return await _offlineFirstService.getSubjectLessons(subjectId);
  }

  /// الحصول على أسئلة مادة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getSubjectQuestions(String subjectId) async {
    return await _offlineFirstService.getSubjectQuestions(subjectId);
  }

  /// الحصول على دروس وحدة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getUnitLessons(String unitId) async {
    return await _offlineFirstService.getUnitLessons(unitId);
  }

  /// الحصول على أسئلة وحدة معينة (فوري من الذاكرة)
  Future<List<dynamic>> getUnitQuestions(String unitId) async {
    return await _offlineFirstService.getUnitQuestions(unitId);
  }

  /// الحصول على أسئلة درس معين (فوري من الذاكرة)
  Future<List<dynamic>> getLessonQuestions(String lessonId) async {
    return await _offlineFirstService.getLessonQuestions(lessonId);
  }

  // ==================== الاشتراكات ====================

  /// التحقق من وجود اشتراك نشط
  bool hasActiveSubscription() {
    return _subscriptionService.hasActiveSubscription();
  }

  /// التحقق من الاشتراك في مادة معينة
  bool isSubscribedToSubject(String subjectId) {
    return _subscriptionService.isSubscribedToSubject(subjectId);
  }

  /// التحقق من الاشتراك في مادة فيديو معينة
  bool isSubscribedToVideoSubject(String videoSubjectId) {
    return _subscriptionService.isSubscribedToVideoSubject(videoSubjectId);
  }

  /// تفعيل كود اشتراك
  Future<bool> activateSubscriptionCode(String code) async {
    try {
      // استخدام الدالة الصحيحة من SubscriptionService
      final result = await _subscriptionService.useSubscriptionCode(code);
      if (result.isSuccess) {
        // إعادة تحميل المحتوى بناءً على الاشتراكات الجديدة
        await _offlineFirstService.onSubscriptionChanged();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في تفعيل كود الاشتراك: $e');
      return false;
    }
  }

  // ==================== الإشعارات ====================

  /// الحصول على جميع الإشعارات
  List<dynamic> get notifications => _notificationsService.notifications;

  /// الحصول على الإشعارات غير المقروءة
  List<dynamic> get unreadNotifications =>
      _notificationsService.unreadNotifications;

  /// عدد الإشعارات غير المقروءة
  int get unreadNotificationsCount => _notificationsService.unreadCount;

  /// وضع علامة مقروء على إشعار
  Future<void> markNotificationAsRead(String notificationId) async {
    await _notificationsService.markAsRead(notificationId);
    notifyListeners();
  }

  /// وضع علامة مقروء على جميع الإشعارات
  Future<void> markAllNotificationsAsRead() async {
    await _notificationsService.markAllAsRead();
    notifyListeners();
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    await _notificationsService.deleteNotification(notificationId);
    notifyListeners();
  }

  /// حذف جميع الإشعارات
  Future<void> clearAllNotifications() async {
    await _notificationsService.clearAllNotifications();
    notifyListeners();
  }

  // ==================== المزامنة ====================

  /// فرض إعادة تحميل جميع البيانات
  Future<void> forceRefreshAll() async {
    try {
      debugPrint('🔄 فرض إعادة تحميل جميع البيانات...');
      await _offlineFirstService.forceRefreshAll();
      notifyListeners();
      debugPrint('✅ تم إعادة تحميل جميع البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تحميل البيانات: $e');
    }
  }

  /// مزامنة البيانات يدوياً
  Future<void> manualSync() async {
    try {
      debugPrint('🔄 بدء المزامنة اليدوية...');
      await _offlineFirstService.forceRefreshAll();
      notifyListeners();
      debugPrint('✅ تم إنجاز المزامنة اليدوية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة اليدوية: $e');
    }
  }

  // ==================== الإحصائيات ====================

  /// الحصول على إحصائيات النظام
  Future<Map<String, dynamic>> getSystemStats() async {
    try {
      final stats = await _offlineFirstService.getSystemStats();
      stats['unreadNotifications'] = unreadNotificationsCount;
      stats['totalNotifications'] = notifications.length;
      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إحصائيات النظام: $e');
      return {};
    }
  }

  // ==================== أدوات المطور ====================

  /// تنظيف جميع البيانات المحلية (للاختبار فقط)
  Future<void> clearAllLocalData() async {
    try {
      debugPrint('🗑️ تنظيف جميع البيانات المحلية...');
      await _notificationsService.clearAllNotifications();
      // يمكن إضافة المزيد من عمليات التنظيف هنا
      debugPrint('✅ تم تنظيف جميع البيانات المحلية');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات المحلية: $e');
    }
  }

  /// إعادة تهيئة النظام
  Future<void> reinitialize() async {
    try {
      debugPrint('🔄 إعادة تهيئة النظام...');
      _isInitialized = false;
      await initialize();
      debugPrint('✅ تم إعادة تهيئة النظام بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تهيئة النظام: $e');
    }
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    _offlineFirstService.dispose();
    super.dispose();
  }

  // ==================== خدمات المزامنة والإشعارات ====================

  /// مزامنة فورية
  Future<void> syncNow() async {
    try {
      await _backgroundSyncService.syncNow();
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة الفورية: $e');
      rethrow;
    }
  }

  /// إعادة تشغيل المزامنة في الخلفية
  Future<void> restartBackgroundSync() async {
    try {
      await _backgroundSyncService.restartBackgroundSync();
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تشغيل المزامنة: $e');
    }
  }

  /// إيقاف المزامنة في الخلفية
  Future<void> stopBackgroundSync() async {
    try {
      await _backgroundSyncService.stopBackgroundSync();
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف المزامنة: $e');
    }
  }

  /// الحصول على FCM token
  String? get fcmToken => _fcmService.fcmToken;

  /// الاشتراك في موضوع FCM
  Future<void> subscribeToFCMTopic(String topic) async {
    try {
      await _fcmService.subscribeToTopic(topic);
    } catch (e) {
      debugPrint('❌ خطأ في الاشتراك في موضوع FCM: $e');
    }
  }

  /// إلغاء الاشتراك من موضوع FCM
  Future<void> unsubscribeFromFCMTopic(String topic) async {
    try {
      await _fcmService.unsubscribeFromTopic(topic);
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الاشتراك من موضوع FCM: $e');
    }
  }

  /// الحصول على حالة النظام
  Map<String, dynamic> get systemStatus => {
    'isInitialized': isInitialized,
    'hasActiveSubscription': hasActiveSubscription(),
    'unreadNotifications': unreadNotificationsCount,
    'backgroundSyncInitialized': _backgroundSyncService.isInitialized,
    'fcmInitialized': _fcmService.isInitialized,
    'fcmToken': fcmToken,
  };
}
