import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/subject_model.dart';
import '../models/unit_model.dart';
import '../models/lesson_model.dart';
import '../models/question_model.dart';
import '../models/video_section_model.dart';
import '../models/video_subject_model.dart';
import '../models/video_unit_model.dart';
import '../models/video_lesson_model.dart';
import '../models/video_model.dart';
import '../../core/models/section.dart';

/// خدمة إدارة بيانات المستخدمين للأدمن
/// تقوم بتحديث بيانات جميع المستخدمين عند إضافة/تعديل/حذف البيانات
class AdminUserDataService {
  static final AdminUserDataService _instance = AdminUserDataService._internal();
  static AdminUserDataService get instance => _instance;
  AdminUserDataService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// تحديث بيانات جميع المستخدمين بعد تغيير البيانات
  Future<void> updateAllUsersData() async {
    try {
      debugPrint('🔄 بدء تحديث بيانات جميع المستخدمين...');

      // جلب جميع البيانات من Firebase
      final allData = await _fetchAllDataFromFirebase();

      // جلب جميع المستخدمين
      final usersSnapshot = await _firestore.collection('user_data').get();
      
      debugPrint('👥 عدد المستخدمين: ${usersSnapshot.docs.length}');

      // تحديث كل مستخدم
      final batch = _firestore.batch();
      int updateCount = 0;

      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data();
        final subscription = userData['subscription'] as Map<String, dynamic>?;
        
        if (subscription != null && subscription['isActive'] == true) {
          // تحديث بيانات المستخدم المشترك
          final updatedUserData = _buildUserData(allData, subscription);
          batch.update(userDoc.reference, updatedUserData);
          updateCount++;
        }
      }

      // تنفيذ التحديثات
      if (updateCount > 0) {
        await batch.commit();
        debugPrint('✅ تم تحديث بيانات $updateCount مستخدم');
      } else {
        debugPrint('ℹ️ لا يوجد مستخدمين نشطين للتحديث');
      }

    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات المستخدمين: $e');
      rethrow;
    }
  }

  /// تحديث بيانات مستخدم واحد بعد تفعيل الاشتراك
  Future<void> updateUserDataAfterSubscription(
    String deviceId,
    Map<String, dynamic> subscription,
  ) async {
    try {
      debugPrint('🔄 تحديث بيانات المستخدم بعد تفعيل الاشتراك: $deviceId');

      // جلب جميع البيانات من Firebase
      final allData = await _fetchAllDataFromFirebase();

      // بناء بيانات المستخدم
      final userData = _buildUserData(allData, subscription);

      // حفظ بيانات المستخدم
      await _firestore.collection('user_data').doc(deviceId).set(userData);

      debugPrint('✅ تم تحديث بيانات المستخدم بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات المستخدم: $e');
      rethrow;
    }
  }

  /// جلب جميع البيانات من Firebase
  Future<Map<String, dynamic>> _fetchAllDataFromFirebase() async {
    try {
      debugPrint('📡 جلب جميع البيانات من Firebase...');

      // جلب جميع البيانات بشكل متوازي لتوفير الوقت
      final futures = await Future.wait([
        _firestore.collection('sections').get(),
        _firestore.collection('subjects').get(),
        _firestore.collection('video_sections').get(),
        _firestore.collection('video_subjects').get(),
        _firestore.collection('units').get(),
        _firestore.collection('lessons').get(),
        _firestore.collection('questions').get(),
        _firestore.collection('video_units').get(),
        _firestore.collection('video_lessons').get(),
        _firestore.collection('videos').get(),
      ]);

      final sectionsSnapshot = futures[0];
      final subjectsSnapshot = futures[1];
      final videoSectionsSnapshot = futures[2];
      final videoSubjectsSnapshot = futures[3];
      final unitsSnapshot = futures[4];
      final lessonsSnapshot = futures[5];
      final questionsSnapshot = futures[6];
      final videoUnitsSnapshot = futures[7];
      final videoLessonsSnapshot = futures[8];
      final videosSnapshot = futures[9];

      debugPrint('✅ تم جلب جميع البيانات من Firebase');
      debugPrint('📊 إحصائيات البيانات:');
      debugPrint('   - الأقسام: ${sectionsSnapshot.docs.length}');
      debugPrint('   - المواد: ${subjectsSnapshot.docs.length}');
      debugPrint('   - أقسام الفيديو: ${videoSectionsSnapshot.docs.length}');
      debugPrint('   - مواد الفيديو: ${videoSubjectsSnapshot.docs.length}');
      debugPrint('   - الوحدات: ${unitsSnapshot.docs.length}');
      debugPrint('   - الدروس: ${lessonsSnapshot.docs.length}');
      debugPrint('   - الأسئلة: ${questionsSnapshot.docs.length}');
      debugPrint('   - وحدات الفيديو: ${videoUnitsSnapshot.docs.length}');
      debugPrint('   - دروس الفيديو: ${videoLessonsSnapshot.docs.length}');
      debugPrint('   - الفيديوهات: ${videosSnapshot.docs.length}');

      return {
        'sections': sectionsSnapshot.docs,
        'subjects': subjectsSnapshot.docs,
        'video_sections': videoSectionsSnapshot.docs,
        'video_subjects': videoSubjectsSnapshot.docs,
        'units': unitsSnapshot.docs,
        'lessons': lessonsSnapshot.docs,
        'questions': questionsSnapshot.docs,
        'video_units': videoUnitsSnapshot.docs,
        'video_lessons': videoLessonsSnapshot.docs,
        'videos': videosSnapshot.docs,
      };
    } catch (e) {
      debugPrint('❌ خطأ في جلب البيانات من Firebase: $e');
      rethrow;
    }
  }

  /// بناء بيانات المستخدم بناءً على اشتراكه
  Map<String, dynamic> _buildUserData(
    Map<String, dynamic> allData,
    Map<String, dynamic> subscription,
  ) {
    try {
      final subscribedSubjectIds = List<String>.from(subscription['subscribedSubjectIds'] ?? []);
      final videoSubjectIds = List<String>.from(subscription['videoSubjectIds'] ?? []);

      debugPrint('🔍 بناء بيانات المستخدم للمواد: $subscribedSubjectIds');
      debugPrint('🔍 بناء بيانات المستخدم لمواد الفيديو: $videoSubjectIds');

      // تحويل البيانات إلى تنسيق محلي
      final sections = _convertSections(allData['sections']);
      final subjects = _convertSubjects(allData['subjects'], subscribedSubjectIds);
      final videoSections = _convertVideoSections(allData['video_sections']);
      final videoSubjects = _convertVideoSubjects(allData['video_subjects'], videoSubjectIds);
      final units = _convertUnits(allData['units'], subscribedSubjectIds);
      final lessons = _convertLessons(allData['lessons'], subscribedSubjectIds);
      final questions = _convertQuestions(allData['questions'], subscribedSubjectIds);
      final videoUnits = _convertVideoUnits(allData['video_units'], videoSubjectIds);
      final videoLessons = _convertVideoLessons(allData['video_lessons'], videoSubjectIds);
      final videos = _convertVideos(allData['videos'], videoSubjectIds);

      return {
        'subscription': subscription,
        'sections': sections,
        'subjects': subjects,
        'video_sections': videoSections,
        'video_subjects': videoSubjects,
        'units': units,
        'lessons': lessons,
        'questions': questions,
        'video_units': videoUnits,
        'video_lessons': videoLessons,
        'videos': videos,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ خطأ في بناء بيانات المستخدم: $e');
      rethrow;
    }
  }

  /// تحويل الأقسام
  List<Map<String, dynamic>> _convertSections(List<QueryDocumentSnapshot> docs) {
    return docs.map((doc) {
      final section = Section.fromFirestore(doc);
      return section.toLocalMap();
    }).toList();
  }

  /// تحويل المواد (المشترك بها فقط)
  List<Map<String, dynamic>> _convertSubjects(
    List<QueryDocumentSnapshot> docs,
    List<String> subscribedSubjectIds,
  ) {
    return docs
        .where((doc) => subscribedSubjectIds.contains(doc.id))
        .map((doc) {
          final subject = Subject.fromFirestore(doc.data() as Map<String, dynamic>, doc.id);
          return subject.toLocalMap();
        }).toList();
  }

  /// تحويل أقسام الفيديو
  List<Map<String, dynamic>> _convertVideoSections(List<QueryDocumentSnapshot> docs) {
    return docs.map((doc) {
      final videoSection = VideoSection.fromFirestore(doc.data() as Map<String, dynamic>, doc.id);
      return videoSection.toLocalMap();
    }).toList();
  }

  /// تحويل مواد الفيديو (المشترك بها فقط)
  List<Map<String, dynamic>> _convertVideoSubjects(
    List<QueryDocumentSnapshot> docs,
    List<String> videoSubjectIds,
  ) {
    return docs
        .where((doc) => videoSubjectIds.contains(doc.id))
        .map((doc) {
          final videoSubject = VideoSubject.fromFirestore(doc.data() as Map<String, dynamic>, doc.id);
          return videoSubject.toLocalMap();
        }).toList();
  }

  /// تحويل الوحدات (للمواد المشترك بها فقط)
  List<Map<String, dynamic>> _convertUnits(
    List<QueryDocumentSnapshot> docs,
    List<String> subscribedSubjectIds,
  ) {
    return docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return subscribedSubjectIds.contains(data['subjectId']);
        })
        .map((doc) {
          final unit = Unit.fromMap({...doc.data() as Map<String, dynamic>, 'id': doc.id});
          return unit.toMap();
        }).toList();
  }

  /// تحويل الدروس (للمواد المشترك بها فقط)
  List<Map<String, dynamic>> _convertLessons(
    List<QueryDocumentSnapshot> docs,
    List<String> subscribedSubjectIds,
  ) {
    return docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return subscribedSubjectIds.contains(data['subjectId']);
        })
        .map((doc) {
          final lesson = Lesson.fromMap({...doc.data() as Map<String, dynamic>, 'id': doc.id});
          return lesson.toMap();
        }).toList();
  }

  /// تحويل الأسئلة (للمواد المشترك بها فقط)
  List<Map<String, dynamic>> _convertQuestions(
    List<QueryDocumentSnapshot> docs,
    List<String> subscribedSubjectIds,
  ) {
    return docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return subscribedSubjectIds.contains(data['subjectId']);
        })
        .map((doc) {
          final question = Question.fromMap({...doc.data() as Map<String, dynamic>, 'id': doc.id});
          return question.toMap();
        }).toList();
  }

  /// تحويل وحدات الفيديو (لمواد الفيديو المشترك بها فقط)
  List<Map<String, dynamic>> _convertVideoUnits(
    List<QueryDocumentSnapshot> docs,
    List<String> videoSubjectIds,
  ) {
    return docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return videoSubjectIds.contains(data['subjectId']);
        })
        .map((doc) {
          final videoUnit = VideoUnit.fromFirestore(doc.data() as Map<String, dynamic>, doc.id);
          return videoUnit.toLocalMap();
        }).toList();
  }

  /// تحويل دروس الفيديو (لمواد الفيديو المشترك بها فقط)
  List<Map<String, dynamic>> _convertVideoLessons(
    List<QueryDocumentSnapshot> docs,
    List<String> videoSubjectIds,
  ) {
    return docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return videoSubjectIds.contains(data['subjectId']);
        })
        .map((doc) {
          final videoLesson = VideoLesson.fromFirestore(doc.data() as Map<String, dynamic>, doc.id);
          return videoLesson.toLocalMap();
        }).toList();
  }

  /// تحويل الفيديوهات (لمواد الفيديو المشترك بها فقط)
  List<Map<String, dynamic>> _convertVideos(
    List<QueryDocumentSnapshot> docs,
    List<String> videoSubjectIds,
  ) {
    return docs
        .where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return videoSubjectIds.contains(data['subjectId']);
        })
        .map((doc) {
          final video = Video.fromFirestore(doc.data() as Map<String, dynamic>, doc.id);
          return video.toLocalMap();
        }).toList();
  }
}
