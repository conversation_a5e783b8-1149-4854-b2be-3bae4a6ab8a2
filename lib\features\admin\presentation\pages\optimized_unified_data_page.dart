import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/adaptive_widgets.dart';
import '../../../../core/utils/adaptive_sizing.dart';
import '../../../../shared/services/optimized_unified_data_service.dart';

/// صفحة إدارة البيانات الموحدة المحسنة
/// النظام الثوري الجديد - توفير 95%+ من المساحة
class OptimizedUnifiedDataPage extends StatefulWidget {
  const OptimizedUnifiedDataPage({Key? key}) : super(key: key);

  @override
  State<OptimizedUnifiedDataPage> createState() =>
      _OptimizedUnifiedDataPageState();
}

class _OptimizedUnifiedDataPageState extends State<OptimizedUnifiedDataPage> {
  final OptimizedUnifiedDataService _service =
      OptimizedUnifiedDataService.instance;

  bool _isGeneratingGeneral = false;
  bool _isGeneratingPaid = false;
  bool _isGeneratingFree = false;

  @override
  void initState() {
    super.initState();
    // طباعة إحصائيات التوفير المتوقعة
    _service.printSavingsExample();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AdaptiveAppBar(
        title: 'البيانات الموحدة المحسنة',
        gradient: AppTheme.primaryGradient,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.adaptiveSpacing),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 24.adaptiveSpacing),
            _buildGeneralDataSection(),
            SizedBox(height: 24.adaptiveSpacing),
            _buildPaidDataSection(),
            SizedBox(height: 24.adaptiveSpacing),
            _buildFreeDataSection(),
            SizedBox(height: 24.adaptiveSpacing),
            _buildInfoSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.adaptiveSpacing),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.rocket_launch,
                color: Colors.white,
                size: 32.adaptiveIcon,
              ),
              SizedBox(width: 12.adaptiveSpacing),
              Expanded(
                child: AdaptiveText(
                  'النظام الثوري الجديد',
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.adaptiveSpacing),
          AdaptiveText(
            'توفير 95%+ من مساحة التخزين و 90%+ من عدد القراءات',
            fontSize: 16,
            color: Colors.white.withOpacity(0.9),
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralDataSection() {
    return _buildSection(
      title: '🌍 الوثيقة العامة',
      description: 'أقسام ومواد (بدون محتوى) - وثيقة واحدة لجميع الطلاب',
      buttonText: 'إنشاء الوثيقة العامة',
      isLoading: _isGeneratingGeneral,
      onPressed: _generateGeneralData,
      color: Colors.blue,
    );
  }

  Widget _buildPaidDataSection() {
    return _buildSection(
      title: '💰 المواد المدفوعة',
      description: 'وثيقة منفصلة لكل مادة مدفوعة (محتوى كامل)',
      buttonText: 'إنشاء وثائق المواد المدفوعة',
      isLoading: _isGeneratingPaid,
      onPressed: _generatePaidData,
      color: Colors.green,
    );
  }

  Widget _buildFreeDataSection() {
    return _buildSection(
      title: '🆓 المواد المجانية',
      description: 'وثيقة منفصلة لكل مادة مجانية (محتوى كامل)',
      buttonText: 'إنشاء وثائق المواد المجانية',
      isLoading: _isGeneratingFree,
      onPressed: _generateFreeData,
      color: Colors.orange,
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required String buttonText,
    required bool isLoading,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(20.adaptiveSpacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        border: Border.all(color: color.withOpacity(0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AdaptiveText(
            title,
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          SizedBox(height: 8.adaptiveSpacing),
          AdaptiveText(description, fontSize: 14, color: Colors.grey[600]),
          SizedBox(height: 16.adaptiveSpacing),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: isLoading ? null : onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: EdgeInsets.symmetric(vertical: 16.adaptiveSpacing),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.adaptiveRadius),
                ),
              ),
              child: isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20.adaptiveIcon,
                          height: 20.adaptiveIcon,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 12.adaptiveSpacing),
                        AdaptiveText(
                          'جاري الإنشاء...',
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ],
                    )
                  : AdaptiveText(
                      buttonText,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: EdgeInsets.all(20.adaptiveSpacing),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16.adaptiveRadius),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryColor,
                size: 24.adaptiveIcon,
              ),
              SizedBox(width: 12.adaptiveSpacing),
              AdaptiveText(
                'مزايا النظام الجديد',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ],
          ),
          SizedBox(height: 16.adaptiveSpacing),
          _buildInfoItem(
            '📄',
            'توفير الوثائق',
            'من 10,000 وثيقة إلى 21 وثيقة فقط (توفير 99.8%)',
          ),
          _buildInfoItem(
            '📖',
            'توفير القراءات',
            'من قراءة واحدة لكل طالب إلى 7 قراءات فقط (توفير 93%)',
          ),
          _buildInfoItem(
            '💾',
            'توفير المساحة',
            'البيانات مخزنة مرة واحدة فقط بدلاً من نسخها لكل طالب',
          ),
          _buildInfoItem(
            '⚡',
            'سرعة التحديث',
            'نظام الإصدارات يتحقق من التحديثات بدون تحميل البيانات',
          ),
          _buildInfoItem(
            '🎯',
            'مرونة الاختيار',
            'الطالب يحمل فقط المواد المشترك بها + المواد المجانية المختارة',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String icon, String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.adaptiveSpacing),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AdaptiveText(icon, fontSize: 20),
          SizedBox(width: 12.adaptiveSpacing),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AdaptiveText(
                  title,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
                SizedBox(height: 4.adaptiveSpacing),
                AdaptiveText(
                  description,
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _generateGeneralData() async {
    setState(() => _isGeneratingGeneral = true);

    try {
      final success = await _service.generateGeneralData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? '✅ تم إنشاء الوثيقة العامة بنجاح'
                  : '❌ فشل في إنشاء الوثيقة العامة',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingGeneral = false);
      }
    }
  }

  Future<void> _generatePaidData() async {
    setState(() => _isGeneratingPaid = true);

    try {
      final success = await _service.generatePaidSubjectsData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? '✅ تم إنشاء وثائق المواد المدفوعة بنجاح'
                  : '❌ فشل في إنشاء وثائق المواد المدفوعة',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingPaid = false);
      }
    }
  }

  Future<void> _generateFreeData() async {
    setState(() => _isGeneratingFree = true);

    try {
      final success = await _service.generateFreeSubjectsData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? '✅ تم إنشاء وثائق المواد المجانية بنجاح'
                  : '❌ فشل في إنشاء وثائق المواد المجانية',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGeneratingFree = false);
      }
    }
  }
}
