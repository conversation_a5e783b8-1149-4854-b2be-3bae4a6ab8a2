import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'persistent_storage_service.dart';

/// نموذج الإشعار المحلي
class LocalNotification {
  final String id;
  final String title;
  final String message;
  final String subjectId;
  final String subjectName;
  final DateTime timestamp;
  final Map<String, int> changes;
  final bool isRead;

  const LocalNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.subjectId,
    required this.subjectName,
    required this.timestamp,
    required this.changes,
    this.isRead = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'timestamp': timestamp.toIso8601String(),
      'changes': changes,
      'isRead': isRead,
    };
  }

  factory LocalNotification.fromMap(Map<String, dynamic> map) {
    return LocalNotification(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      subjectId: map['subjectId'] ?? '',
      subjectName: map['subjectName'] ?? '',
      timestamp: DateTime.parse(
        map['timestamp'] ?? DateTime.now().toIso8601String(),
      ),
      changes: Map<String, int>.from(map['changes'] ?? {}),
      isRead: map['isRead'] ?? false,
    );
  }

  LocalNotification copyWith({
    String? id,
    String? title,
    String? message,
    String? subjectId,
    String? subjectName,
    DateTime? timestamp,
    Map<String, int>? changes,
    bool? isRead,
  }) {
    return LocalNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      subjectId: subjectId ?? this.subjectId,
      subjectName: subjectName ?? this.subjectName,
      timestamp: timestamp ?? this.timestamp,
      changes: changes ?? this.changes,
      isRead: isRead ?? this.isRead,
    );
  }
}

/// خدمة الإشعارات المحلية
/// تولد إشعارات محلية عند تحديث محتوى المواد
class LocalNotificationsService extends ChangeNotifier {
  static final LocalNotificationsService _instance =
      LocalNotificationsService._internal();
  static LocalNotificationsService get instance => _instance;
  LocalNotificationsService._internal();

  final PersistentStorageService _persistentStorage =
      PersistentStorageService.instance;
  List<LocalNotification> _notifications = [];

  List<LocalNotification> get notifications => _notifications;
  List<LocalNotification> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();
  int get unreadCount => unreadNotifications.length;

  /// تهيئة الخدمة وتحميل الإشعارات المحفوظة
  Future<void> initialize() async {
    try {
      await _loadNotifications();
      debugPrint('✅ تم تهيئة خدمة الإشعارات المحلية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// تحميل الإشعارات من التخزين المحلي
  Future<void> _loadNotifications() async {
    try {
      await _persistentStorage.initialize();
      final storageDir = _persistentStorage.storageDirectory;
      final file = File('${storageDir.path}/local_notifications.json');

      if (!await file.exists()) {
        _notifications = [];
        return;
      }

      final content = await file.readAsString();
      final List<dynamic> data = jsonDecode(content);
      _notifications = data
          .map((item) => LocalNotification.fromMap(item))
          .toList();

      // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      debugPrint('📱 تم تحميل ${_notifications.length} إشعار محلي');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإشعارات: $e');
      _notifications = [];
    }
  }

  /// حفظ الإشعارات في التخزين المحلي
  Future<void> _saveNotifications() async {
    try {
      await _persistentStorage.initialize();
      final storageDir = _persistentStorage.storageDirectory;
      final file = File('${storageDir.path}/local_notifications.json');

      final data = _notifications
          .map((notification) => notification.toMap())
          .toList();
      await file.writeAsString(jsonEncode(data));

      debugPrint('✅ تم حفظ ${_notifications.length} إشعار محلي');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الإشعارات: $e');
    }
  }

  /// إنشاء إشعار تحديث المحتوى
  Future<void> createContentUpdateNotification({
    required String subjectId,
    required String subjectName,
    required Map<String, dynamic> oldContent,
    required Map<String, dynamic> newContent,
  }) async {
    try {
      final changes = _calculateChanges(oldContent, newContent);

      if (changes.isEmpty) {
        debugPrint('ℹ️ لا توجد تغييرات في المحتوى للمادة: $subjectName');
        return;
      }

      final message = _buildUpdateMessage(subjectName, changes);

      final notification = LocalNotification(
        id: '${subjectId}_${DateTime.now().millisecondsSinceEpoch}',
        title: '📚 تحديث محتوى المادة',
        message: message,
        subjectId: subjectId,
        subjectName: subjectName,
        timestamp: DateTime.now(),
        changes: changes,
      );

      _notifications.insert(0, notification);
      await _saveNotifications();

      debugPrint('🔔 تم إنشاء إشعار تحديث للمادة: $subjectName');
      debugPrint('📝 التغييرات: $changes');

      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار التحديث: $e');
    }
  }

  /// حساب التغييرات بين المحتوى القديم والجديد
  Map<String, int> _calculateChanges(
    Map<String, dynamic> oldContent,
    Map<String, dynamic> newContent,
  ) {
    final changes = <String, int>{};

    // حساب تغييرات الوحدات
    final oldUnits = oldContent['units'] as List? ?? [];
    final newUnits = newContent['units'] as List? ?? [];
    final unitsDiff = newUnits.length - oldUnits.length;
    if (unitsDiff != 0) {
      changes['units'] = unitsDiff;
    }

    // حساب تغييرات الدروس
    final oldLessons = oldContent['lessons'] as List? ?? [];
    final newLessons = newContent['lessons'] as List? ?? [];
    final lessonsDiff = newLessons.length - oldLessons.length;
    if (lessonsDiff != 0) {
      changes['lessons'] = lessonsDiff;
    }

    // حساب تغييرات الأسئلة
    final oldQuestions = oldContent['questions'] as List? ?? [];
    final newQuestions = newContent['questions'] as List? ?? [];
    final questionsDiff = newQuestions.length - oldQuestions.length;
    if (questionsDiff != 0) {
      changes['questions'] = questionsDiff;
    }

    return changes;
  }

  /// بناء رسالة التحديث
  String _buildUpdateMessage(String subjectName, Map<String, int> changes) {
    final List<String> parts = [];

    changes.forEach((type, count) {
      final String typeName;
      switch (type) {
        case 'units':
          typeName = count == 1 ? 'وحدة' : 'وحدات';
          break;
        case 'lessons':
          typeName = count == 1 ? 'درس' : 'دروس';
          break;
        case 'questions':
          typeName = count == 1 ? 'سؤال' : 'أسئلة';
          break;
        default:
          typeName = type;
      }

      if (count > 0) {
        parts.add('تم إضافة ${count.abs()} $typeName');
      } else if (count < 0) {
        parts.add('تم حذف ${count.abs()} $typeName');
      }
    });

    final changesText = parts.join(' و ');
    return '$changesText لمادة $subjectName';
  }

  /// وضع علامة مقروء على إشعار
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        await _saveNotifications();
        notifyListeners();
        debugPrint('✅ تم وضع علامة مقروء على الإشعار: $notificationId');
      }
    } catch (e) {
      debugPrint('❌ خطأ في وضع علامة مقروء: $e');
    }
  }

  /// وضع علامة مقروء على جميع الإشعارات
  Future<void> markAllAsRead() async {
    try {
      _notifications = _notifications
          .map((notification) => notification.copyWith(isRead: true))
          .toList();
      await _saveNotifications();
      notifyListeners();
      debugPrint('✅ تم وضع علامة مقروء على جميع الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في وضع علامة مقروء على جميع الإشعارات: $e');
    }
  }

  /// حذف إشعار
  Future<void> deleteNotification(String notificationId) async {
    try {
      _notifications.removeWhere((n) => n.id == notificationId);
      await _saveNotifications();
      notifyListeners();
      debugPrint('✅ تم حذف الإشعار: $notificationId');
    } catch (e) {
      debugPrint('❌ خطأ في حذف الإشعار: $e');
    }
  }

  /// حذف جميع الإشعارات
  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      await _saveNotifications();
      notifyListeners();
      debugPrint('✅ تم حذف جميع الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في حذف جميع الإشعارات: $e');
    }
  }

  /// حذف الإشعارات القديمة (أكثر من 30 يوم)
  Future<void> cleanupOldNotifications() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final oldCount = _notifications.length;

      _notifications.removeWhere(
        (notification) => notification.timestamp.isBefore(cutoffDate),
      );

      if (_notifications.length != oldCount) {
        await _saveNotifications();
        notifyListeners();
        debugPrint('🗑️ تم حذف ${oldCount - _notifications.length} إشعار قديم');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الإشعارات القديمة: $e');
    }
  }

  /// الحصول على إشعارات مادة معينة
  List<LocalNotification> getNotificationsBySubject(String subjectId) {
    return _notifications
        .where((notification) => notification.subjectId == subjectId)
        .toList();
  }

  /// الحصول على آخر إشعار لمادة معينة
  LocalNotification? getLatestNotificationForSubject(String subjectId) {
    final subjectNotifications = getNotificationsBySubject(subjectId);
    return subjectNotifications.isNotEmpty ? subjectNotifications.first : null;
  }
}
