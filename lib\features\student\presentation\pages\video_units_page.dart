import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/video_unit_model.dart';
import '../../../../shared/services/video_service.dart';
// النظام الجديد Offline-First
import '../../../../shared/services/unified_offline_service.dart';
import 'video_lessons_page.dart';

/// صفحة وحدات الفيديوهات للطالب - مُعاد كتابتها بالكامل
class VideoUnitsPage extends StatefulWidget {
  final String subjectId;
  final bool hasSubscription;

  const VideoUnitsPage({
    super.key,
    required this.subjectId,
    required this.hasSubscription,
  });

  @override
  State<VideoUnitsPage> createState() => _VideoUnitsPageState();
}

class _VideoUnitsPageState extends State<VideoUnitsPage> {
  final UnifiedOfflineService _offlineService = UnifiedOfflineService.instance;
  final VideoService _videoService = VideoService.instance;

  List<VideoUnit> _units = [];
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadVideoUnitsImmediately();
  }

  /// تحميل فوري لوحدات الفيديو من النظام الجديد
  Future<void> _loadVideoUnitsImmediately() async {
    try {
      // استخدام النظام القديم مؤقتاً حتى يتم تطبيق النظام الجديد بالكامل
      await _loadSavedDataFirst();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل وحدات الفيديو: $e');
    }
  }

  /// تحميل البيانات المحفوظة أولاً
  Future<void> _loadSavedDataFirst() async {
    try {
      // فحص البيانات المحلية من الكاش أولاً (بدون await)
      final cachedUnits = _videoService.getCachedVideoUnits(widget.subjectId);
      final activeCachedUnits = cachedUnits
          .where((unit) => unit.isActive)
          .toList();

      if (activeCachedUnits.isNotEmpty) {
        // البيانات متوفرة في الكاش - عرضها فوراً
        setState(() {
          _units = activeCachedUnits;
        });
        debugPrint(
          '⚡ إرجاع ${activeCachedUnits.length} وحدة فيديو من الكاش للمادة ${widget.subjectId}',
        );
        debugPrint(
          '🚀 تم عرض ${activeCachedUnits.length} وحدة فيديو من البيانات المحفوظة',
        );

        return;
      }

      // إذا لم توجد بيانات في الكاش، حاول تحميل البيانات من Firebase
      debugPrint('📱 لا توجد وحدات في الكاش، محاولة تحميل من Firebase...');

      // محاولة تحميل البيانات من Firebase
      try {
        final savedUnits = await _videoService.getVideoUnits(widget.subjectId);
        final activeSavedUnits = savedUnits
            .where((unit) => unit.isActive)
            .toList();

        setState(() {
          _units = activeSavedUnits;
        });

        if (activeSavedUnits.isNotEmpty) {
          debugPrint(
            '✅ تم تحميل ${activeSavedUnits.length} وحدة فيديو من Firebase',
          );
        } else {
          debugPrint('📱 لا توجد وحدات نشطة للمادة ${widget.subjectId}');
        }
      } catch (e) {
        debugPrint('❌ خطأ في تحميل البيانات من Firebase: $e');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات: $e');
    }
  }

  // تم إزالة وظيفة التحديث الأولى - التحديث يتم من الصفحة الرئيسية فقط

  /// تحديث يدوي (Pull-to-Refresh) - سيتم استبداله بالنظام الجديد
  Future<void> _refreshData() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      debugPrint('🔄 تحديث يدوي لوحدات الفيديوهات...');
      final units = await _videoService.refreshVideoUnitsFromFirebase(
        widget.subjectId,
      );
      final activeUnits = units.where((unit) => unit.isActive).toList();

      if (mounted) {
        setState(() {
          _units = activeUnits;
          _isRefreshing = false;
        });
        debugPrint('✅ تم تحديث ${activeUnits.length} وحدة فيديو يدوياً');
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحديث اليدوي: $e');
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في التحديث: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'الوحدات',
        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
      ),
      foregroundColor: Colors.white,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _isRefreshing ? null : _refreshData,
          tooltip: 'تحديث',
        ),
      ],
    );
  }

  Widget _buildBody() {
    // النظام الجديد: عرض البيانات مباشرة مع إمكانية التحديث
    return RefreshIndicator(
      onRefresh: _refreshVideoUnits,
      child: _units.isNotEmpty ? _buildUnitsContent() : _buildEmptyState(),
    );
  }

  Widget _buildUnitsContent() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 20.h),
            Expanded(child: _buildUnitsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: AppTheme.secondaryGradient,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(Icons.folder_open, color: Colors.white, size: 24.sp),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'وحدات الفيديوهات',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'اختر الوحدة لمشاهدة الفيديوهات التعليمية',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitsList() {
    return ListView.builder(
      itemCount: _units.length,
      itemBuilder: (context, index) {
        final unit = _units[index];
        return _buildUnitCard(unit);
      },
    );
  }

  Widget _buildUnitCard(VideoUnit unit) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16.r),
          onTap: () => _navigateToLessons(unit),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                _buildUnitIcon(unit),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        unit.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                      if (unit.description.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          unit.description,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textSecondaryColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.textSecondaryColor,
                  size: 16.sp,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUnitIcon(VideoUnit unit) {
    return Container(
      width: 50.w,
      height: 50.w,
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Icon(Icons.folder_open, color: Colors.white, size: 24.sp),
    );
  }

  void _navigateToLessons(VideoUnit unit) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoLessonsPage(
          unitId: unit.id,
          hasSubscription: widget.hasSubscription,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open_outlined,
            size: 80.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد وحدات فيديوهات متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة الوحدات قريباً',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
