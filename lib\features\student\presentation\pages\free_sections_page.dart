import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/models/section.dart';
import '../../../../shared/services/section_service.dart';
import '../../../../shared/services/data_distribution_service.dart';
// النظام الجديد Offline-First
import 'subjects_by_section_page.dart';

/// صفحة الأقسام المجانية للاختبارات
class FreeSectionsPage extends StatefulWidget {
  const FreeSectionsPage({super.key});

  @override
  State<FreeSectionsPage> createState() => _FreeSectionsPageState();
}

class _FreeSectionsPageState extends State<FreeSectionsPage> {
  final DataDistributionService _dataService = DataDistributionService.instance;
  final SectionService _sectionService = SectionService.instance;

  List<Section> _freeSections = [];
  // bool _isRefreshing = false; // تم إزالة نظام التحديث

  @override
  void initState() {
    super.initState();
    _loadFreeSectionsImmediately();
  }

  /// تحميل فوري للأقسام المجانية من النظام الجديد
  Future<void> _loadFreeSectionsImmediately() async {
    try {
      // تحميل الأقسام من خدمة فرز البيانات الجديدة
      final freeSections = _dataService.getFreeSections();

      setState(() {
        _freeSections = freeSections;
      });

      debugPrint(
        '⚡ تم تحميل ${freeSections.length} قسم مجاني فوراً بالنظام الجديد',
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام المجانية: $e');
      // fallback للنظام القديم
      await _loadFreeSections();
    }
  }

  Future<void> _loadFreeSections() async {
    try {
      await _sectionService.loadSections();
      final freeSections = _sectionService.freeSections;

      setState(() {
        _freeSections = freeSections;
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الأقسام المجانية: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الأقسام المجانية: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'تجربة التطبيق',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            // رسالة ترحيبية
            Container(
              width: double.infinity,
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade400, Colors.green.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(Icons.quiz_outlined, size: 48.sp, color: Colors.white),
                  SizedBox(height: 12.h),
                  Text(
                    'مرحباً بك في تجربة التطبيق',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'هنا ستجد عدد صغير من الأسئلة كعينة مجانية، لذا قم بتفعيل اشتراك لتستمتع بآلاف الأسئلة وبمزايا كثيرة متاحة في القسم المدفوع',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // قائمة الأقسام المجانية
            Expanded(
              child: _freeSections.isEmpty
                  ? _buildEmptyState()
                  : _buildSectionsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionsList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: _freeSections.length,
      itemBuilder: (context, index) {
        final section = _freeSections[index];
        return _buildFreeSectionCard(section);
      },
    );
  }

  Widget _buildFreeSectionCard(Section section) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SubjectsBySectionPage(
                section: section,
                isFreeAccess: true, // الأقسام المجانية متاحة للجميع
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              colors: [Colors.green.shade400, Colors.green.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // أيقونة القسم
              Container(
                width: 60.w,
                height: 60.h,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Icon(
                  Icons.quiz_outlined,
                  size: 30.sp,
                  color: Colors.white,
                ),
              ),
              SizedBox(width: 16.w),

              // معلومات القسم
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      section.name,
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    if (section.description.isNotEmpty) ...[
                      SizedBox(height: 4.h),
                      Text(
                        section.description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    SizedBox(height: 8.h),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(
                        'مجاني',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // سهم التنقل
              Icon(Icons.arrow_forward_ios, color: Colors.white, size: 20.sp),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.quiz_outlined, size: 80.sp, color: Colors.grey[400]),
          SizedBox(height: 16.h),
          Text(
            'لا توجد أقسام مجانية متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم إضافة أقسام مجانية قريباً',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }
}
