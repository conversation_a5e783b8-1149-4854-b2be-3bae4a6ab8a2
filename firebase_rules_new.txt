// قواعد Firebase الجديدة للنظام المحدث
// يجب نسخ هذه القواعد إلى Firebase Console

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // قواعد بيانات المستخدمين - للقراءة الواحدة
    match /user_data/{deviceId} {
      // السماح للجميع بالقراءة (للطلاب والإدارة)
      allow read: if true;
      // السماح بالكتابة للإدارة فقط أو للمستخدم نفسه
      allow write: if request.auth != null;
    }
    
    // الأقسام - قراءة للجميع، كتابة للإدارة فقط
    match /sections/{sectionId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // المواد - قراءة للجميع، كتابة للإدارة فقط
    match /subjects/{subjectId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // الوحدات - قراءة للجميع، كتابة للإدارة فقط
    match /units/{unitId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // الدروس - قراءة للجميع، كتابة للإدارة فقط
    match /lessons/{lessonId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // الأسئلة - قراءة للجميع، كتابة للإدارة فقط
    match /questions/{questionId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // أقسام الفيديو - قراءة للجميع، كتابة للإدارة فقط
    match /video_sections/{videoSectionId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // مواد الفيديو - قراءة للجميع، كتابة للإدارة فقط
    match /video_subjects/{videoSubjectId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // وحدات الفيديو - قراءة للجميع، كتابة للإدارة فقط
    match /video_units/{videoUnitId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // دروس الفيديو - قراءة للجميع، كتابة للإدارة فقط
    match /video_lessons/{videoLessonId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // الفيديوهات - قراءة للجميع، كتابة للإدارة فقط
    match /videos/{videoId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // الاشتراكات - قراءة للجميع، كتابة للإدارة فقط
    match /subscriptions/{subscriptionId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // أكواد التفعيل - قراءة للجميع، كتابة للإدارة فقط
    match /activation_codes/{codeId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // إعدادات التطبيق - قراءة للجميع، كتابة للإدارة فقط
    match /app_settings/{settingId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // أي مجموعة أخرى - نفس القواعد
    match /{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
