{"indexes": [{"collectionGroup": "lessons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "video_lessons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "videos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lessonId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "videos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "units", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "video_units", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "subjectId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}], "fieldOverrides": []}